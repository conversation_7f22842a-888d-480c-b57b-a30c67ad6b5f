import type { Row } from "@tanstack/react-table";
import { But<PERSON> } from "@netpro/design-system";
import { json, useLoaderData, useLocation, useNavigation } from "@remix-run/react";
import { Download, Filter } from "lucide-react";
import queryString from "query-string";
import { type ReactNode, useState } from "react";
import { CardContainer } from "~/components/CardContainer";
import { EnhancedTable } from "~/components/EnhancedTable";
import { EnhancedTableContainer } from "~/components/EnhancedTableContainer";
import { FilterRow } from "~/components/FilterRow";
import { Form } from "~/components/Form";
import { FormColumnsFilter } from "~/components/FormColumnsFilter";
import { FormCombobox } from "~/components/FormCombobox";
import { FormDatePicker } from "~/components/FormDatePicker";
import { FormSearch } from "~/components/FormSearch";
import { LinkButton } from "~/components/ui/buttons/LinkButton";
import { LoadingState } from "~/components/ui/filters/LoadingState";
import { useExportSubmissionDataColumns } from "~/features/basic-financial-report/utilities/export-submission-data-columns";
import { searchSchema } from "~/features/simplified-tax-return/schemas/searchSchema";
import { authHeaders } from "~/lib/auth/utils/auth-headers";
import { useFilterForm } from "~/lib/hooks/useFilterForm";
import { makeEnhancedLoader } from "~/lib/makeEnhancedLoader.server";
import { middleware } from "~/lib/middlewares.server";
import { getFilterParams } from "~/lib/utilities/get-filter-params";
import { Modules } from "~/lib/utilities/modules";
import { requireActiveModule } from "~/lib/utilities/require-active-modules";
import type { SubmissionDTO } from "~/services/api-generated";
import { managementPanamaListSubmissionsByModule } from "~/services/api-generated";

export const handle = {
  breadcrumb: {
    label: "Export Submission Data",
    to: "/basic-financial-report/export-submission-data",
  },
  title: "Export Submission Data",
}

export const loader = makeEnhancedLoader(async ({ request, queryString }) => {
  await middleware(["auth"], request);
  const { module: bfrModule } = await requireActiveModule({ request, key: Modules.BASIC_FINANCIAL_REPORT });
  const schemaData = searchSchema.safeParse(queryString).data
  const { pageNumber, pageSize, order, orderDirection } = await getFilterParams({ request });
  const isExported = schemaData?.isExported === "true" ? true : schemaData?.isExported === "false" ? false : undefined
  const { data: paginatedSubmissions, error } = await managementPanamaListSubmissionsByModule({ headers: await authHeaders(request), query: {
    IsPaid: true,
    ModuleId: bfrModule.id,
    PageNumber: pageNumber,
    GeneralSearchTerm: schemaData?.search,
    SortOrder: orderDirection,
    // Correctly typing order would require it to be present in the schema as an enum which is too strict for our goals
    SortBy: order as any,
    Country: schemaData?.country,
    PageSize: pageSize,
    SubmittedAfterDate: schemaData?.submittedAfter,
    SubmittedBeforeDate: schemaData?.submittedBefore,
    IsExported: isExported,
  } });

  if (error) {
    throw new Response("Currently unable to retrieve Basic financial reports submissions", { status: 412 });
  }

  return json({
    paginatedSubmissions,
  });
}, { authorize: ["bfr.panama.submissions.export"] })

export default function BasicFinancialReportExportSubmissionData(): ReactNode {
  const exportSubmissionDataColumns = useExportSubmissionDataColumns()
  const { paginatedSubmissions: { data: submissions, totalItemCount } } = useLoaderData<typeof loader>();
  const navigation = useNavigation();
  const location = useLocation()
  const { formMethods } = useFilterForm(searchSchema);
  const [rowSelection, setRowSelection] = useState<Record<string, boolean>>({});
  const usingAccountingRecordsOnlyOptions = [{ label: "All", value: "all" }, { label: "Yes", value: "true" }, { label: "No", value: "false" }]
  const queryParams = queryString.stringify({
    submissionIds: Object.keys(rowSelection),
    location: location.pathname,
  }, { arrayFormat: "bracket" })

  return (
    <CardContainer>
      <Form formMethods={formMethods}>
        <FilterRow cols={4}>
          <FormDatePicker name="submittedAfter" label="Submitted After" />
          <FormDatePicker name="submittedBefore" label="Submitted Before" />
          <FormCombobox
            name="isExported"
            label="Show Exported"
            options={usingAccountingRecordsOnlyOptions}
          />
          <FormColumnsFilter label="Visible Columns" columns={exportSubmissionDataColumns} />
        </FilterRow>
        <FilterRow>
          <div className="col-span-full flex flex-row items-center gap-2">
            <FormSearch
              name="search"
              formItemProps={{ className: "w-full" }}
              inputProps={{ placeholder: "Search entity name, master client, referral office, etc." }}
            />
            <Button size="sm" className="gap-1.5" type="submit">
              <Filter size={14} />
              Apply Filter(s)
            </Button>
            <LinkButton
              buttonProps={{
                type: "button",
                variant: "link",
                size: "sm",
                disabled: Object.keys(rowSelection).length === 0,
                className: "gap-1.5",
              }}
              linkProps={{
                to: {
                  pathname: "/basic-financial-report/export-submission-data/file",
                  search: queryParams,
                },
                reloadDocument: true,
              }}
            >
              Export as XLSX
              <Download className="size-4 mr-2" />
            </LinkButton>
          </div>
        </FilterRow>
      </Form>
      <EnhancedTableContainer>
        <EnhancedTable
          data={submissions}
          loading={<LoadingState isLoading={navigation.state === "loading"} />}
          rowId="id"
          reactTableOptions={{
            state: {
              rowSelection,
            },
            onRowSelectionChange: setRowSelection,
          }}
          onRowClick={(row: Row<SubmissionDTO>) => {
            setRowSelection((prev) => {
              const copy = { ...prev };
              // If the row was selected, action was to deselect it, so remove it from the list.
              row.getIsSelected() ? delete copy[row.id] : (copy[row.id] = true);

              return copy;
            });
          }}
          columns={exportSubmissionDataColumns}
          totalItems={totalItemCount}
        />
      </EnhancedTableContainer>
    </CardContainer>
  );
}
