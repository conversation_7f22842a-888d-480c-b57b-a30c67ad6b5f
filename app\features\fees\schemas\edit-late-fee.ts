import type { Zod<PERSON>awShape } from "zod";
import { isAfter } from "date-fns";
import { z } from "zod";
import { nonNullDate } from "~/lib/utilities/zod-validators";

const base = z.object({
  financialYear: z.coerce.string({ required_error: "Please provide a year." }).min(4, { message: "Please provide a year." }).max(4, { message: "Please provide a valid year." }).pipe(z.coerce.number()),
  amount: z.coerce.number().int({
    message: "Fee must be an integer",
  }).max(9999, {
    message: "Fee must be less than 10000",
  }).gt(0, "Fee must be greater than 0").optional(),
  description: z.string().optional(),
  invoiceText: z.string().optional(),
})

function makeSchema(schema: ZodRawShape) {
  return base.extend(schema).refine(data => isAfter(data.endAt, data.startAt), {
    message: "Start date cannot be later than the end date",
    path: ["startAt"],
  });
}

export const editLateFeeSchemaClient = makeSchema({
  startAt: nonNullDate({ message: "Please provide a start date" }).default(null),
  endAt: nonNullDate({ message: "Please provide an end date" }).default(null),
})

export const editLateFeeSchemaServer = makeSchema({
  startAt: z.string().pipe(z.coerce.date().transform(data => data.toISOString())),
  endAt: z.string().pipe(z.coerce.date().transform(data => data.toISOString())),
})

export type EditLateFeeSchemaType = z.infer<typeof editLateFeeSchemaClient>;
