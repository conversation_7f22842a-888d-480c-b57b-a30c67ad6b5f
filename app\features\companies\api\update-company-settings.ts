import type { UpdateCompanySettingsParams } from "../types/company";
import type { ApiClientResponse } from "~/features/master-clients/types/generic";
import { client } from "~/lib/api-client";
import type { ClientRequestHeaders } from "~/lib/types/client-request-headers";
import { translateErrorResponse } from "~/lib/utilities/translate-error-response";

export async function updateCompanySettings({
  companyId,
  key,
  accessToken,
  userId,
  data,
}: UpdateCompanySettingsParams & ClientRequestHeaders): Promise<ApiClientResponse<{ success: true }>> {
  try {
    await client.post(
      `/management/companies/${companyId}/settings/${key}`,
      accessToken,
      userId,
      data,
    );
  } catch (ResponseError) {
    return await translateErrorResponse(ResponseError);
  }

  return {
    result: {
      success: true,
    },
    error: null,
  };
}
