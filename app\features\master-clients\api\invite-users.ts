import type { TypedResponse } from "@remix-run/node";
import type { MasterClientUser } from "~/features/master-clients/api/get-master-clients";
import type { ApiClientResponse, AuthenticatedApiRequest } from "~/features/master-clients/types/generic";
import { client } from "~/lib/api-client";
import { translateErrorResponse } from "~/lib/utilities/translate-error-response";

type Props = {
  masterClientId: string
  userIds: MasterClientUser["id"][]
} & AuthenticatedApiRequest

type Response = ApiClientResponse<{ success: true }>

/**
 * masterClientId param is currently unused, but it is passed on the implementation of the endpoint.
 */
export async function inviteUsers({ userId, accessToken, userIds }: Props): Promise<Response> {
  try {
    await client.post<TypedResponse>(
      "/management/users/invitations",
      accessToken,
      userId,
      {
        UserIds: userIds,
      },
    );
  } catch (ResponseError) {
    return await translateErrorResponse(ResponseError);
  }

  return {
    result: {
      success: true,
    },
    error: null,
  };
}
