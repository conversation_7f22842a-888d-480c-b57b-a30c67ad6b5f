import { Checkbox } from "@netpro/design-system";
import { createColumnHelper } from "@tanstack/react-table";
import { ReadableSubmissionStatusNames } from "~/features/submissions/utilities/submission-status";
import { useFormatColDate } from "~/lib/hooks/useFormatColDate";
import type { ListSubmissionBahamasDTO } from "~/services/api-generated";

type Type = "submissions" | "payments" | "ird-export"

/**
 * This is a helper hook for the table column definitions.
 */
export function useEsTbahColumns(type: Type) {
  const formatColDate = useFormatColDate();
  const columnHelper = createColumnHelper<ListSubmissionBahamasDTO>();
  let columns = [];
  let sortableColumns: string[] = [];
  const sortingEnabled = (column: string): boolean => sortableColumns.map(col => col.toLowerCase()).includes(column.toLowerCase());

  switch (type) {
    case "submissions":
      /**
       * Sortable columns as per API definition:
       * Available values : LegalEntityName, LegalEntityCode, LegalEntityVPCode, MasterClientCode, Status, FinancialYear, CreatedAt, ExportedAt, PaymentMethod, PaymentReceivedAt, PaymentReference
       */
      sortableColumns = [
        "CreatedByEmail",
        "LegalEntityName",
        "LegalEntityCode",
        "InitialSubmittedAt",
        "LegalEntityVPCode",
        "MasterClientCode",
        "Status",
        "FinancialYear",
        "SubmittedAt",
        "CreatedAt",
        "IncorporationDate",
        "ExportedAt",
        "PaymentMethod",
        "PaymentReceivedAt",
        "PaymentReference",
        "PaymentReceivedAt",
        "LegalEntityReferralOffice",
        "PaymentReference",
        "FinancialPeriodEndsAt",
        "ReopenedAt",
      ]

      columns = [
        columnHelper.accessor("createdByEmail", {
          header: "Email",
          id: "createdByEmail",
          enableSorting: sortingEnabled("createdByEmail"),
        }),
        columnHelper.accessor("legalEntityName", {
          header: "Entity Name",
          id: "legalEntityName",
          enableSorting: sortingEnabled("legalEntityName"),
        }),
        columnHelper.accessor("legalEntityCode", {
          header: "Regulatory Code",
          id: "legalEntityCode",
          enableSorting: sortingEnabled("legalEntityCode"),
        }),
        columnHelper.accessor("masterClientCode", {
          header: "Master Client Code",
          id: "masterClientCode",
          enableSorting: sortingEnabled("masterClientCode"),
        }),
        columnHelper.accessor("status", {
          header: "Status",
          id: "status",
          enableSorting: sortingEnabled("status"),
          cell: data => data.row.original.status ? ReadableSubmissionStatusNames[data.row.original.status] : data.row.original.status,
        }),
        columnHelper.accessor("createdAt", {
          header: "Date Created",
          id: "createdAt",
          enableSorting: sortingEnabled("createdAt"),
          cell: formatColDate("createdAt", { timezone: "Bahamas" }),
        }),
        columnHelper.accessor("submittedAt", {
          header: "Latest Submitted",
          id: "submittedAt",
          enableSorting: sortingEnabled("submittedAt"),
          cell: (data) => {
            const submittedAt = data.row.original.submittedAt;
            const initialSubmittedAt = data.row.original.initialSubmittedAt;

            if (submittedAt === initialSubmittedAt) {
              return "";
            }

            return submittedAt ? formatColDate("submittedAt", { timezone: "Bahamas" })(data as any) : "";
          },
        }),
        columnHelper.accessor("initialSubmittedAt", {
          header: "Initial Submitted",
          id: "initialSubmittedAt",
          enableSorting: sortingEnabled("initialSubmittedAt"),
          cell: formatColDate("initialSubmittedAt", { timezone: "Bahamas" }),
        }),
        columnHelper.accessor("reopenedAt", {
          header: "Re-Opened Date",
          id: "reopenedAt",
          enableSorting: sortingEnabled("reopenedAt"),
          cell: (data) => {
            const reopenedAt = data.row.original.reopenedAt;

            // Only show if there are multiple revisions (reopenedAt will be null if only 1 revision)
            if (!reopenedAt) {
              return ""; // No reopening happened - only 1 revision
            }

            return formatColDate("reopenedAt", { timezone: "Bahamas" })(data as any);
          },
        }),
        columnHelper.accessor("incorporationDate", {
          header: "Incorporation Date",
          id: "incorporationDate",
          enableSorting: sortingEnabled("incorporationDate"),
          cell: formatColDate("incorporationDate", { timezone: "UTC" }), // UTC since it's a date only
        }),
        columnHelper.accessor("paymentReceivedAt", {
          header: "Paid Date",
          id: "paymentReceivedAt",
          enableSorting: sortingEnabled("paymentReceivedAt"),
          cell: formatColDate("paymentReceivedAt", { timezone: "Bahamas" }),
        }),
        columnHelper.accessor("paymentReference", {
          header: "Payment Ref",
          id: "paymentReference",
          enableSorting: sortingEnabled("paymentReference"),
        }),
        columnHelper.accessor("financialPeriodEndsAt", {
          header: "Financial Period End Date",
          id: "financialPeriodEndsAt",
          enableSorting: sortingEnabled("financialPeriodEndsAt"),
          cell: formatColDate("financialPeriodEndsAt", { timezone: "UTC" }), // UTC since it's a date only
        }),
        columnHelper.accessor("legalEntityReferralOffice", {
          header: "Referral Office",
          id: "legalEntityReferralOffice",
          enableSorting: sortingEnabled("legalEntityReferralOffice"),
        }),
        columnHelper.accessor("hasActivityNone", {
          header: "None",
          id: "hasActivityNone",
          enableSorting: sortingEnabled("hasActivityNone"),
          cell: data => data.row.original.hasActivityNone ? "Yes" : "No",
        }),
        columnHelper.accessor("hasActivityBankingBusiness", {
          header: "BB",
          id: "hasActivityBankingBusiness",
          enableSorting: sortingEnabled("hasActivityBankingBusiness"),
          cell: data => data.row.original.hasActivityBankingBusiness ? "Yes" : "No",
        }),
        columnHelper.accessor("hasActivityInsuranceBusiness", {
          header: "IB",
          id: "hasActivityInsuranceBusiness",
          enableSorting: sortingEnabled("hasActivityInsuranceBusiness"),
          cell: data => data.row.original.hasActivityInsuranceBusiness ? "Yes" : "No",
        }),
        columnHelper.accessor("hasActivityFundManagementBusiness", {
          header: "FMB",
          id: "hasActivityFundManagementBusiness",
          enableSorting: sortingEnabled("hasActivityFundManagementBusiness"),
          cell: data => data.row.original.hasActivityFundManagementBusiness ? "Yes" : "No",
        }),
        columnHelper.accessor("hasActivityFinanceLeasingBusiness", {
          header: "FLB",
          id: "hasActivityFinanceLeasingBusiness",
          enableSorting: sortingEnabled("hasActivityFinanceLeasingBusiness"),
          cell: data => data.row.original.hasActivityFinanceLeasingBusiness ? "Yes" : "No",
        }),
        columnHelper.accessor("hasActivityHeadquartersBusiness", {
          header: "HQ",
          id: "hasActivityHeadquartersBusiness",
          enableSorting: sortingEnabled("hasActivityHeadquartersBusiness"),
          cell: data => data.row.original.hasActivityHeadquartersBusiness ? "Yes" : "No",
        }),
        columnHelper.accessor("hasActivityShippingBusiness", {
          header: "SB",
          id: "hasActivityShippingBusiness",
          enableSorting: sortingEnabled("hasActivityShippingBusiness"),
          cell: data => data.row.original.hasActivityShippingBusiness ? "Yes" : "No",
        }),
        columnHelper.accessor("hasActivityIntellectualPropertyBusiness", {
          header: "IP",
          id: "hasActivityIntellectualPropertyBusiness",
          enableSorting: sortingEnabled("hasActivityIntellectualPropertyBusiness"),
          cell: data => data.row.original.hasActivityIntellectualPropertyBusiness ? "Yes" : "No",
        }),
        columnHelper.accessor("hasActivityHoldingBusiness", {
          header: "SC",
          id: "hasActivityHoldingBusiness",
          enableSorting: sortingEnabled("hasActivityHoldingBusiness"),
          cell: data => data.row.original.hasActivityHoldingBusiness ? "Yes" : "No",
        }),
      ];
      break;
    case "payments":
      /**
       * Sortable columns as per API definition:
       * Available values : LegalEntityName, LegalEntityCode, LegalEntityVPCode, MasterClientCode, Status, FinancialYear, CreatedAt, ExportedAt, PaymentMethod, PaymentReceivedAt, PaymentReference
       */
      sortableColumns = [
        "LegalEntityName",
        "LegalEntityCode",
        "LegalEntityVPCode",
        "MasterClientCode",
        "Status",
        "FinancialYear",
        "CreatedAt",
        "SubmittedAt",
        "ExportedAt",
        "PaymentMethod",
        "PaymentReceivedAt",
        "PaymentReference",
        "FinancialPeriodEndsAt",
      ]

      columns = [
        columnHelper.display({
          id: "select",
          header: ({ table }) => (
            <Checkbox
              checked={
                table.getIsAllPageRowsSelected()
                || (table.getIsSomePageRowsSelected() && "indeterminate")
              }
              onCheckedChange={(value) => {
                if (value) {
                  table.toggleAllPageRowsSelected(true);
                } else {
                  table.resetRowSelection(false);
                }
              }}
              className="-translate-x-3 mx-1"
              aria-label="Select all"
            />
          ),
          cell: ({ row }) => (
            <Checkbox
              checked={row.getIsSelected()}
              onCheckedChange={value => row.toggleSelected(Boolean(value))}
              aria-label="Select row"
              className="-translate-x-3 mx-1"
            />
          ),
          enableSorting: false,
          enableHiding: false,
        }),
        columnHelper.accessor("legalEntityName", {
          header: "Entity Name",
          id: "legalEntityName",
          enableSorting: sortingEnabled("legalEntityName"),
        }),
        columnHelper.accessor("legalEntityCode", {
          header: "Regulatory Code",
          id: "legalEntityCode",
          enableSorting: sortingEnabled("legalEntityCode"),
        }),
        columnHelper.accessor("masterClientCode", {
          header: "Master Client Code",
          id: "masterClientCode",
          enableSorting: sortingEnabled("masterClientCode"),
        }),
        columnHelper.accessor("status", {
          header: "Status",
          id: "status",
          enableSorting: sortingEnabled("status"),
        }),
        columnHelper.accessor("financialPeriodEndsAt", {
          header: "Financial Period End Date",
          id: "financialPeriodEndsAt",
          enableSorting: sortingEnabled("financialPeriodEndsAt"),
          cell: formatColDate("financialPeriodEndsAt", { timezone: "UTC" }), // UTC since it's a date only
        }),
        columnHelper.accessor("createdAt", {
          header: "Date Created",
          id: "createdAt",
          enableSorting: sortingEnabled("createdAt"),
          cell: formatColDate("createdAt", { timezone: "Bahamas" }),
        }),
        columnHelper.accessor("submittedAt", {
          header: "Submitted Date",
          id: "submittedAt",
          enableSorting: sortingEnabled("submittedAt"),
          cell: formatColDate("submittedAt", { timezone: "Bahamas" }),
        }),
      ];
      break;
    case "ird-export":
      /**
       * Sortable columns as per API definition:
       * Available values : LegalEntityName, LegalEntityCode, LegalEntityVPCode, MasterClientCode, Status, FinancialYear, CreatedAt, ExportedAt, PaymentMethod, PaymentReceivedAt, PaymentReference
       */
      sortableColumns = [
        "LegalEntityName",
        "LegalEntityCode",
        "LegalEntityVPCode",
        "MasterClientCode",
        "Status",
        "FinancialYear",
        "CreatedAt",
        "SubmittedAt",
        "ExportedAt",
        "PaymentMethod",
        "PaymentReceivedAt",
        "PaymentReference",
        "FinancialPeriodStartsAt",
        "FinancialPeriodEndsAt",
        "ResubmittedAt",
      ]
      columns = [
        columnHelper.display({
          id: "select",
          header: ({ table }) => (
            <Checkbox
              checked={
                table.getIsAllPageRowsSelected()
                || (table.getIsSomePageRowsSelected() && "indeterminate")
              }
              onCheckedChange={(value) => {
                if (value) {
                  table.toggleAllPageRowsSelected(true);
                } else {
                  table.resetRowSelection(false);
                }
              }}
              className="-translate-x-3 mx-1"
              aria-label="Select all"
            />
          ),
          cell: ({ row }) => (
            <Checkbox
              checked={row.getIsSelected()}
              onCheckedChange={value => row.toggleSelected(Boolean(value))}
              aria-label="Select row"
              className="-translate-x-3 mx-1"
            />
          ),
          enableSorting: false,
          enableHiding: false,
        }),
        columnHelper.accessor("legalEntityName", {
          id: "LegalEntityName",
          header: "Entity Name",
          enableSorting: sortingEnabled("LegalEntityName"),
        }),
        columnHelper.accessor("legalEntityCode", {
          id: "LegalEntityCode",
          header: "Regulatory Code",
          enableSorting: sortingEnabled("LegalEntityCode"),
        }),
        columnHelper.accessor("status", {
          id: "Status",
          header: "Status",
          enableSorting: sortingEnabled("Status"),
        }),
        columnHelper.accessor("submittedAt", {
          header: "Submitted Date",
          id: "SubmittedAt",
          enableSorting: sortingEnabled("SubmittedAt"),
          cell: formatColDate("submittedAt", { timezone: "Bahamas" }),
        }),
        columnHelper.accessor("exportedAt", {
          header: "Exported At",
          id: "exportedAt",
          enableSorting: sortingEnabled("exportedAt"),
          cell: formatColDate("exportedAt", { timezone: "Bahamas" }),
        }),
        columnHelper.accessor("initialSubmittedAt", {
          header: "Resubmitted Date",
          id: "submittedAt",
          enableSorting: sortingEnabled("submittedAt"),
          cell: formatColDate("submittedAt", { timezone: "Bahamas" }),
        }),
        columnHelper.accessor("financialPeriodStartsAt", {
          header: "Financial Period Start Date",
          id: "financialPeriodStartsAt",
          enableSorting: sortingEnabled("financialPeriodStartsAt"),
          cell: formatColDate("financialPeriodStartsAt", { timezone: "UTC" }), // UTC since it's a date only
        }),
        columnHelper.accessor("financialPeriodEndsAt", {
          header: "Financial Period End Date",
          id: "financialPeriodEndsAt",
          enableSorting: sortingEnabled("financialPeriodEndsAt"),
          cell: formatColDate("financialPeriodEndsAt", { timezone: "UTC" }), // UTC since it's a date only
        }),
        columnHelper.accessor("paymentMethod", {
          header: "Payment Method",
          id: "paymentMethod",
          enableSorting: sortingEnabled("paymentMethod"),
        }),
        columnHelper.accessor("paymentReceivedAt", {
          header: "Paid Date",
          id: "paymentReceivedAt",
          enableSorting: sortingEnabled("paymentReceivedAt"),
          cell: formatColDate("paymentReceivedAt", { timezone: "Bahamas" }),
        }),
        columnHelper.accessor("paymentReference", {
          header: "Payment Ref",
          id: "paymentReference",
          enableSorting: sortingEnabled("paymentReference"),
        }),
      ]
  }

  return { columns }
}
