import type { ReactNode } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { Button, Form as NetProForm } from "@netpro/design-system";
import { Outlet, Form as RemixForm, useLoaderData, useParams, useSubmit } from "@remix-run/react";
import { formatDateForAPI } from "~/lib/utilities/format";
import { FormProvider, useForm } from "react-hook-form";
import { ActionSheetActionRow } from "~/components/ActionSheetActionRow";
import { ActionSheetContent } from "~/components/ActionSheetContent";
import { ActionSheetFooter } from "~/components/ActionSheetFooter";
import { ActionSheetSection } from "~/components/ActionSheetSection";
import { FormDatePicker } from "~/components/FormDatePicker";
import { FormInput } from "~/components/FormInput";
import type { EditLateFeeSchemaType } from "~/features/fees/schemas/edit-late-fee";
import { editLateFeeSchema } from "~/features/fees/schemas/edit-late-fee";
import { authHeaders } from "~/lib/auth/utils/auth-headers";
import { makeEnhancedAction } from "~/lib/makeEnhancedAction.server";
import { makeEnhancedLoader } from "~/lib/makeEnhancedLoader.server";
import { middleware } from "~/lib/middlewares.server";
import { Jurisdictions } from "~/lib/utilities/jurisdictions";
import { requireActiveJurisdiction } from "~/lib/utilities/require-active-jurisdiction";
import type { SettingsDTO, STRLatePaymentFeeDTO } from "~/services/api-generated";
import { getJurisdictionSettings, setJurisdictionSettings } from "~/services/api-generated";

export const action = makeEnhancedAction(async ({ request, params, setNotification, redirect }) => {
  await middleware(["auth"], request);
  const { jurisdiction: strJurisdiction } = await requireActiveJurisdiction({ request, code: Jurisdictions.NEVIS });
  const formData = await request.formData();
  const formAction = formData.get("_action")
  let body: SettingsDTO = {}
  if (formAction === "update") {
    const data = JSON.parse(formData.get("data") as string) as EditLateFeeSchemaType;
    body = {
      strLatePaymentFeeSettings: {
        strLatePaymentFees: [
          {
            id: params.id,
            ...data,
          },
        ],
      },
    }
  }

  if (formAction === "delete") {
    body = { strLatePaymentFeeSettings: { strLatePaymentFees: [{ id: params.id, financialYear: null }] } }
  }

  const { error } = await setJurisdictionSettings({
    headers: await authHeaders(request),
    path: { jurisdictionId: strJurisdiction.id || "" },
    body,
  });

  if (error) {
    setNotification({ title: "Failed to update the Late fee", variant: "error" })
    console.warn({ title: "Failed to update the Late fee", variant: error });
  } else {
    const message = formAction === "update" ? "Updated Late fee successfully" : "Late fee payment has been deleted"

    setNotification({ title: message, variant: "success" })
  }

  return redirect("/simplified-tax-return/fees")
}, { authorize: ["str.late-payments.set"] });

export const loader = makeEnhancedLoader(async ({ request, json }) => {
  await middleware(["auth"], request);
  const { jurisdiction: strJurisdiction } = await requireActiveJurisdiction({ request, code: Jurisdictions.NEVIS });
  const feeDataLate = await getJurisdictionSettings({ headers: await authHeaders(request), path: { jurisdictionId: strJurisdiction.id || "" } })
  if (!feeDataLate) {
    throw new Response("Fee STR not found", { status: 404 });
  }

  return json(feeDataLate.data?.strLatePaymentFeeSettings);
}, { authorize: ["str.late-payments.set"] });

export default function StrFeeLateUpdate(): ReactNode {
  const params = useParams();
  const submit = useSubmit()
  const { strLatePaymentFees } = useLoaderData<typeof loader>();
  const feeDetails = strLatePaymentFees?.find(fee => fee.id === params.id);
  const formMethods = useForm<EditLateFeeSchemaType>({
    resolver: zodResolver(editLateFeeSchema),
    defaultValues: {
      description: feeDetails?.description || "Late filing fee",
      invoiceText: feeDetails?.invoiceText || "Late filing fee",
      startAt: feeDetails?.startAt || undefined,
      endAt: feeDetails?.endAt || undefined,
      amount: feeDetails?.amount || undefined,
      financialYear: feeDetails?.financialYear || undefined,
    },
  });
  const handleDelete = (): void => {
    submit({ _action: "delete" }, { method: "post" })
  };

  function onSubmit(data: EditLateFeeSchemaType) {
    const { description, invoiceText, startAt, endAt, financialYear, amount } = data;
    const startDate = formatDateForAPI(startAt);
    const endDate = formatDateForAPI(endAt);
    const body = JSON.stringify({
      ...data,
      startAt: startDate,
      endAt: endDate
    });
    submit({ _action: "update", data: body }, { method: "post" })
  }

  return (
    <>
      <FormProvider {...formMethods}>
        <NetProForm {...formMethods}>
          <RemixForm onSubmit={formMethods.handleSubmit(onSubmit)}>
            <ActionSheetContent title="Update Late Fee Payment">
              <ActionSheetSection title="Details">
                <FormInput name="financialYear" label="Financial Year*" inputProps={{ placeholder: "Year", type: "number", ...formMethods.register("financialYear"), disabled: true }} />
                <FormDatePicker name="startAt" label="Fee Start Date*" />
                <FormDatePicker name="endAt" label="Fee End Date*" />
                <FormInput name="amount" label="Fee*" inputProps={{ placeholder: "0.00", type: "number", ...formMethods.register("amount") }} />
              </ActionSheetSection>
              <ActionSheetSection title="Action" className="flex-row py-5">
                <ActionSheetActionRow label="Remove the STR Late Fee">
                  <Button size="sm" variant="destructive" onClick={handleDelete} type="button">Delete</Button>
                </ActionSheetActionRow>
              </ActionSheetSection>
            </ActionSheetContent>
            <ActionSheetFooter>
              <Button size="sm" type="submit">Update</Button>
            </ActionSheetFooter>
          </RemixForm>
        </NetProForm>
      </FormProvider>
      <Outlet />
    </>
  )
}
