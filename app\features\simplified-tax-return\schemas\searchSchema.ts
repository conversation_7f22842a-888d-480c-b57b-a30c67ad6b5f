import { z } from "zod";
import { optionalDateString } from "~/lib/utilities/zod-validators";

export const searchSchema = z.object({
  columns: z.string().array().optional(),
  filingYear: z.string().optional(),
  isExported: z.string().optional(),
  submittedAfter: optionalDateString,
  submittedBefore: optionalDateString,
  country: z.string().optional(),
  search: z.string().optional(),
  location: z.string().optional(),
});

export type SearchSchemaType = z.infer<typeof searchSchema>
