import type { ActionFunctionArgs, LoaderFunctionArgs, TypedResponse } from "@remix-run/node";
import { json, redirect } from "@remix-run/node";
import { Outlet, useFetcher, useNavigate } from "@remix-run/react";
import { useEffect } from "react";
import { signIn } from "~/features/users/api/put-sign-in";
import { getClientCredentialsToken } from "~/lib/auth/utils/authentication.server";
import { commitSession, getSession } from "~/lib/auth/utils/session.server";

export async function action({ request }: ActionFunctionArgs): Promise<TypedResponse> {
  const session = await getSession(request.headers.get("Cookie"));
  // Get session variables from Microsoft Entra authentication result
  const objectId = session.get("objectId") as string;
  const { accessToken, expiresOn } = await getClientCredentialsToken();

  // Set the accessToken to call the APIs
  session.set("accessToken", accessToken);
  session.set("accessTokenExpiresOn", expiresOn);

  // Sign in the user
  const user = await signIn({
    data: { objectId },
    accessToken,
    userId: "",
  });
  const { id, isActive, displayName, email } = user;

  session.set("isActive", isActive);
  session.set("userId", id);
  session.set("userName", displayName);
  session.set("userEmail", email);

  const redirectUrl = session.get("redirect") || "/dashboard";

  return json({ redirectUrl }, {
    headers: { "Set-Cookie": await commitSession(session) },
  });
}

export async function loader({ request }: LoaderFunctionArgs) {
  const session = await getSession(request.headers.get("Cookie"));
  // Get session variables from Microsoft Entra authentication result
  const objectId = session.get("objectId") as string;
  if (!objectId) {
    throw redirect("/login");
  }

  return null;
}

export default function Layout() {
  const fetcher = useFetcher<{
    redirectUrl?: string
  }>();
  const navigate = useNavigate();

  // Trigger the POST action
  useEffect(() => {
    if (fetcher.state === "idle") {
      fetcher.submit({}, { method: "post", action: "/auth/application" });
    }
  }, [fetcher]);

  // Redirect once the fetcher finishes
  useEffect(() => {
    if (fetcher.data?.redirectUrl) {
      navigate(fetcher.data.redirectUrl);
    }
  }, [fetcher.data, navigate]);

  return <Outlet />;
}
