import { z } from "zod";
import { SubmissionStatusNamesEnum } from "~/features/submissions/utilities/submission-status";
import { optionalDateString, stringBoolean } from "~/lib/utilities/zod-validators";
import { RelevantActivity } from "./relevant-activities";

export const searchSchema = z.object({
  columns: z.string().array().optional(),
  submittedAfterDate: optionalDateString,
  submittedBeforeDate: optionalDateString,
  financialPeriodStartAt: optionalDateString,
  financialPeriodEndAt: optionalDateString,
  relevantActivities: z.array(z.nativeEnum(RelevantActivity)).optional(),
  isPaid: z.string().optional(),
  showSubmitted: stringBoolean().optional(),
  allowReopen: stringBoolean().optional(),
  search: z.string().optional(),
  isExported: z.enum(["true", "false", ""]).optional().nullable(),
  isDeleted: stringBoolean().optional().default("false"),
});

export const submissionsSearchSchema = searchSchema.merge(z.object({
  companyIncorporatedAfterDate: optionalDateString,
  companyIncorporatedBeforeDate: optionalDateString,
  paidAfterDate: optionalDateString,
  paidBeforeDate: optionalDateString,
  status: z.union([z.nativeEnum(SubmissionStatusNamesEnum), z.literal("")]).optional(),
}))

export type SearchSchemaType = z.infer<typeof searchSchema>
export type SubmissionsSearchSchemaType = z.infer<typeof submissionsSearchSchema>
