import {
  unstable_createM<PERSON>oryU<PERSON><PERSON><PERSON><PERSON><PERSON> as createMemoryUp<PERSON><PERSON><PERSON><PERSON>,
  unstable_parseMultipartFormData as parseMultipartFormData,
} from "@remix-run/node";
import AnnouncementForm from "~/features/announcements/components/forms/AnnouncementForm";
import type { AnnouncementSchemaType } from "~/features/announcements/schema/announcement-schema";
import { authHeaders } from "~/lib/auth/utils/auth-headers";
import { makeEnhancedAction } from "~/lib/makeEnhancedAction.server";
import { makeEnhancedLoader } from "~/lib/makeEnhancedLoader.server";
import { middleware } from "~/lib/middlewares.server";
import type { CreateUpdateAnnouncementDTO, ManagementCreateAnnouncementDocumentData } from "~/services/api-generated";
import { getJurisdictions, getMasterClients, managementAnnouncementCreate, managementCreateAnnouncementDocument } from "~/services/api-generated";

export const handle = {
  breadcrumb: {
    label: "Create Announcement",
    to: "/announcements/new",
  },
  title: "Create Announcement",
}

export const loader = makeEnhancedLoader(async ({ json, request }) => {
  await middleware(["auth"], request);
  const { data: jurisdictions, error: jurisdictionError } = await getJurisdictions({ headers: await authHeaders(request), query: { pageNumber: 1, pageSize: 100 } });
  const { data: masterClients, error: masterClientError } = await getMasterClients({ headers: await authHeaders(request), query: { pageNumber: 1, pageSize: 100 } })

  if (jurisdictionError) {
    throw new Response("Failed to fetch jurisdictions", { status: 500 });
  }

  if (masterClientError) {
    throw new Response("Failed to fetch master clients", { status: 500 });
  }

  if (!jurisdictions.data) {
    throw new Response("Jurisdictions not found", { status: 404 });
  }

  if (!masterClients.data) {
    throw new Response("Master clients not found", { status: 404 });
  }

  jurisdictions.data.unshift({ id: "all", name: "All" })

  return json({
    options: {
      jurisdictions: jurisdictions.data,
      masterClients: masterClients.data,
    },
  })
}, { authorize: ["announcements.create"] })

type AnnouncementCreationFormData = AnnouncementSchemaType & { sendAt: string, masterClientCodes: string }
export const action = makeEnhancedAction(async ({ request, redirect, setNotification }) => {
  await middleware(["auth"], request);
  const maxPartSize = 5 * 1024 * 1024; // 5MB in bytes
  const uploadHandler = createMemoryUploadHandler({
    maxPartSize,
  });
  const formData = await parseMultipartFormData(request, uploadHandler);
  const { subject, emailSubject, sendNow, sendAt, sendToAllMasterClients, masterClientCodes, jurisdictionId, body }
   = Object.fromEntries(formData) as unknown as AnnouncementCreationFormData
  const createAnnouncementBody: CreateUpdateAnnouncementDTO = {
    subject,
    emailSubject,
    sendNow: sendNow === "true",
    sendToAllMasterClients: sendToAllMasterClients === "true",
    body,
  }
  const files = formData.getAll("files") as File[];
  const headers = await authHeaders(request);

  if (!createAnnouncementBody.sendNow) {
    console.log("sendAt", sendAt);
    createAnnouncementBody.sendAt = sendAt
  }

  if (createAnnouncementBody.sendToAllMasterClients) {
    if (jurisdictionId === "all") {
      createAnnouncementBody.sendToAllJurisdictions = true
    } else {
      createAnnouncementBody.jurisdictionId = jurisdictionId
    }
  }

  if (!createAnnouncementBody.sendToAllMasterClients) {
    createAnnouncementBody.masterClientCodes = JSON.parse(masterClientCodes)
  }

  if (files.length > 0) {
    createAnnouncementBody.includeAttachments = true
  }

  const { data: announcement, error } = await managementAnnouncementCreate({ headers, body: createAnnouncementBody })

  if (error) {
    throw new Response(error.exceptionMessage as string, { status: 500 })
  }

  const announcementId = announcement instanceof Blob ? await announcement.text() : announcement;

  if (files.length > 0) {
    const formattedFiles: ManagementCreateAnnouncementDocumentData["body"][] = files.map((file, index) => ({
      File: file,
      UploadComplete: index === files.length - 1,
    }));
    // Create an array of promises for all file uploads
    const uploadPromises = formattedFiles.map(formattedFile =>
      managementCreateAnnouncementDocument({
        headers,
        path: { announcementId },
        body: formattedFile,
      }),
    );

    // Wait for all uploads to complete concurrently
    await Promise.all(uploadPromises);
  }

  setNotification({ title: "Success", message: "Announcement created successfully", variant: "success" })

  return redirect("/announcements")
}, {
  authorize: ["announcements.create"],
});

export type CreateAnnouncementLoaderData = typeof loader;

export default function CreateAnnouncement() {
  return (
    <AnnouncementForm type="create" />
  );
}
