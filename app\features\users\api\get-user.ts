import { client } from "~/lib/api-client";

type Args = {
  id: string
  userId: string
  accessToken: string
}

export type GetUserResponse = {
  name: string
  surname: string
  username: string
  displayName: string
  email: string
  roleNames: any[]
  roleIds: any[]
  isActive: boolean
  isBlocked: boolean
  applicationUserRoles: any[]
  objectId: string
  id: string
}

export function getUser({ id, userId, accessToken }: Args): Promise<GetUserResponse> {
  return client.get<GetUserResponse>(
    `/management/users/${id}`,
    accessToken,
    userId,
  );
}
