import type { ColumnDef } from "@tanstack/react-table";
import { formatColDate } from "~/lib/utilities/format-col-date";
import type { ListAnnouncementDTO } from "~/services/api-generated";

export const announcementColumns: ColumnDef<ListAnnouncementDTO>[] = [
  { id: "sendAt", header: "Scheduled Date", accessorKey: "sendAt", cell: formatColDate("sendAt"), enableSorting: true },
  { id: "subject", header: "Subject", accessorKey: "subject", enableSorting: true },
  { id: "masterClients", header: "Master Clients", accessorKey: "masterClientIds", enableSorting: true },
  { id: "jurisdictions", header: "Jurisdiction", accessorKey: "jurisdictionIds", enableSorting: true },
  { id: "status", header: "Status", accessorKey: "status", enableSorting: true },
]
