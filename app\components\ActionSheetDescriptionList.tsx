import type { ActionSheetHeaderProps } from "./ActionSheetHeader";
import { Fragment, type JSX } from "react";

type Data = Record<string, any>

type Props<TData extends Data> = {
  actionSheetHeaderProps?: ActionSheetHeaderProps
  headers: [headerKey: keyof TData, headerText: string, formatter?: ((row: TData) => string)][]
}

export function ActionSheetDescriptionList<TData extends Data>({ data, headers }: { data: TData } & Props<TData>): JSX.Element {
  return (
    <dl className="w-full grid grid-cols-2">
      {headers.map(([headerKey, headerText, formatter], i) => {
        return (
        // Assuming the headers is a constant
        // eslint-disable-next-line react/no-array-index-key
          <Fragment key={i}>
            <dt className="text-sm">{headerText}</dt>
            <dd className="text-sm text-right font-semibold justify-self-end">{formatter ? formatter(data) : data[headerKey]}</dd>
          </Fragment>
        )
      })}
    </dl>
  )
}
