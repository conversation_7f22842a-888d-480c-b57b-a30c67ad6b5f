import type { FieldErrors } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Di<PERSON>Footer, <PERSON><PERSON>Header, <PERSON><PERSON><PERSON><PERSON>le, Di<PERSON>Trigger, notify, Spinner, <PERSON>area } from "@netpro/design-system";
import { Form, useFetcher, useParams } from "@remix-run/react";
import { useEffect } from "react";
import { getValidatedFormData, useRemixForm } from "remix-hook-form";
import { declineCompany } from "~/features/companies/api/decline-company";
import type { DeclineCompanySchemaType } from "~/features/companies/schemas/decline-company";
import { declineCompanySchema } from "~/features/companies/schemas/decline-company";
import { usePreserveQueryNavigate } from "~/lib/hooks/usePreserveQueryNavigate";
import { makeEnhancedAction } from "~/lib/makeEnhancedAction.server";
import { makeEnhancedLoader } from "~/lib/makeEnhancedLoader.server";
import { middleware } from "~/lib/middlewares.server";

export const loader = makeEnhancedLoader(async ({ request }) => {
  await middleware(["auth"], request);

  return null;
}, {
  authorize: ["companies.onboarding.reject"],
});

export const action = makeEnhancedAction(async ({ request, json, params }) => {
  const { userId, accessToken } = await middleware(["auth"], request);
  const { id } = params;

  if (!id) {
    throw new Response("Not Found", { status: 404 });
  }

  const { errors, data } = await getValidatedFormData<DeclineCompanySchemaType>(request, zodResolver(declineCompanySchema));

  if (errors) {
    return json({ errors });
  }

  await declineCompany({ companyId: id, accessToken, userId, data: {
    declineReason: data.reason,
  } });

  return json({ success: true });
}, {
  authorize: ["companies.onboarding.reject"],
});

export default function DeclineCompany(): JSX.Element {
  const params = useParams();
  const navigate = usePreserveQueryNavigate();
  const fetcher = useFetcher<{
    success: boolean
    errors?: FieldErrors<DeclineCompanySchemaType>
  }>();
  const { handleSubmit, formState: { errors, isSubmitting }, register } = useRemixForm<DeclineCompanySchemaType>({
    mode: "onSubmit",
    resolver: zodResolver(declineCompanySchema),
    submitConfig: {
      action: `/companies/pending-onboardings/${params.id}/decline`,
    },
    fetcher,
  });

  useEffect(() => {
    if (fetcher.data && !fetcher.data.errors) {
      navigate("/companies/pending-onboardings", {
        replace: true,
      });
      notify({ message: "Company declined successfully", variant: "success" });
    }
  }, [fetcher.data, navigate]);

  return (
    <Dialog open onOpenChange={() => navigate(`/companies/pending-onboardings/${params.id}`)}>
      <DialogTrigger />
      <DialogContent>
        <Form method="POST" onSubmit={handleSubmit} noValidate className="grid gap-4">
          <DialogHeader>
            <DialogTitle>
              Please provide a reason for declining this company
            </DialogTitle>
          </DialogHeader>
          <div>
            <Textarea placeholder="Decline reason" {...register("reason")} disabled={isSubmitting} invalid={Boolean(errors?.reason)} />
            {errors?.reason && <p className="text-red-600">{errors.reason.message}</p>}
          </div>
          <DialogFooter className="flex gap-x-2">
            <Button
              disabled={isSubmitting}
              variant="outline"
              type="button"
              onClick={() => navigate(`/companies/pending-onboardings/${params.id}`)}
            >
              Cancel
            </Button>
            <Button disabled={isSubmitting} variant="destructive" type="submit">
              {isSubmitting ? <Spinner className="size-4 mx-0 text-white" /> : "Decline"}
            </Button>
          </DialogFooter>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
