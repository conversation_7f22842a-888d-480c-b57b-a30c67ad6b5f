import type { Row } from "@tanstack/react-table";
import { Button, SelectItem } from "@netpro/design-system";
import { useLoaderData, useLocation, useNavigation, useSearchParams } from "@remix-run/react";
import { Download, Filter } from "lucide-react";
import queryString from "query-string";
import { type ReactNode, useState } from "react";
import { Authorized } from "~/components/Authorized";
import { CardContainer } from "~/components/CardContainer";
import { EnhancedTable } from "~/components/EnhancedTable";
import { EnhancedTableContainer } from "~/components/EnhancedTableContainer";
import { FilterRow } from "~/components/FilterRow";
import { Form } from "~/components/Form";
import { FormColumnsFilter } from "~/components/FormColumnsFilter";
import { FormDatePicker } from "~/components/FormDatePicker";
import { FormSearch } from "~/components/FormSearch";
import { FormSelect } from "~/components/FormSelect";
import { LinkButton } from "~/components/ui/buttons/LinkButton";
import { LoadingState } from "~/components/ui/filters/LoadingState";
import { UpdatePaymentStatusDialog } from "~/features/dialogs/payments/UpdatePaymentStatusDialog";
import { useEsTbahColumns } from "~/features/economic-substance-tbah/hooks/use-es-tbah-columns";
import type { SubmissionsSearchSchemaType } from "~/features/economic-substance-tbah/types/search-schema";
import { submissionsSearchSchema } from "~/features/economic-substance-tbah/types/search-schema";
import { authHeaders } from "~/lib/auth/utils/auth-headers";
import { useFilterForm } from "~/lib/hooks/useFilterForm";
import { makeEnhancedAction } from "~/lib/makeEnhancedAction.server";
import { makeEnhancedLoader } from "~/lib/makeEnhancedLoader.server";
import { middleware } from "~/lib/middlewares.server";
import { getFilterParams } from "~/lib/utilities/get-filter-params";
import { Modules } from "~/lib/utilities/modules";
import { requireActiveModule } from "~/lib/utilities/require-active-modules";
import type {
  ListSubmissionBahamasDTOPaginatedResponse,
  MarkSubmissionsAsPaidRequestDTO,
  SubmissionDTO,
  SubmissionStatus,
} from "~/services/api-generated";
import { managementBahamasListSubmissionsByModule, managementMarkAsPaid } from "~/services/api-generated";

export const handle = {
  breadcrumb: {
    label: "Economic Substance",
    to: "/economic-substance/ird-export",
  },
  title: "IRD Export",
}

export const loader = makeEnhancedLoader(async ({ request, queryString, setNotification, json }) => {
  await middleware(["auth"], request);
  const { module: strModule } = await requireActiveModule({ request, key: Modules.ECONOMIC_SUBSTANCE_BAHAMAS });
  const schemaData = submissionsSearchSchema.safeParse(queryString).data
  const { pageNumber, pageSize, order, orderDirection } = await getFilterParams({ request });
  const { data: paginatedSubmissions, error } = await managementBahamasListSubmissionsByModule({ headers: await authHeaders(request), query: {
    ModuleId: strModule.id,
    PageNumber: pageNumber,
    SortOrder: orderDirection,
    SortBy: order as any,
    PageSize: pageSize,
    SubmittedAfterDate: schemaData?.submittedAfterDate,
    SubmittedBeforeDate: schemaData?.submittedBeforeDate,
    CompanyIncorporatedAfterDate: schemaData?.companyIncorporatedAfterDate,
    CompanyIncorporatedBeforeDate: schemaData?.companyIncorporatedBeforeDate,
    FinancialPeriodStartAt: schemaData?.financialPeriodStartAt,
    FinancialPeriodEndAt: schemaData?.financialPeriodEndAt,
    PaidAfterDate: schemaData?.paidAfterDate,
    PaidBeforeDate: schemaData?.paidBeforeDate,
    RelevantActivities: schemaData?.relevantActivities as string[] | undefined,
    Status: schemaData?.status as SubmissionStatus,
    ShowSubmitted: schemaData?.showSubmitted === "true" ? true : schemaData?.showSubmitted === "false" ? false : undefined,
    AllowReopen: schemaData?.allowReopen === "true" ? true : schemaData?.allowReopen === "false" ? false : undefined,
    GeneralSearchTerm: schemaData?.search,
    IsExported: schemaData?.isExported === "true" ? true : schemaData?.isExported === "false" ? false : undefined,
  } })

  if (error) {
    if (error.code === 8) {
      // Invalid sorting response
      setNotification({
        title: "Invalid data sorting",
        message: `Cannot sort the table by colum ${order}.`,
      })

      return json({
        paginatedSubmissions: { data: [], totalItemCount: 0 } as ListSubmissionBahamasDTOPaginatedResponse,
      })
    }

    // Unhandled API error
    console.error("Error fetching submissions", error);
    throw new Response("Currently unable to retrieve Economic Substance Bahamas submissions", { status: 412 });
  }

  return json({
    paginatedSubmissions,
  });
}, { authorize: ["es.bahamas.submissions.view"] })

export const action = makeEnhancedAction(async ({ request, setNotification, json }) => {
  const formData = await request.formData()
  const data = formData.get("data") as string
  const body = JSON.parse(data) as MarkSubmissionsAsPaidRequestDTO
  const { error } = await managementMarkAsPaid({ body })

  if (error) {
    setNotification({ title: "Failed at updating payment status", variant: "error" })
  } else {
    const numberOfSubmissions = body.submissionIds?.length
    if (numberOfSubmissions) {
      let alertMessage = ""

      if (numberOfSubmissions === 1) {
        alertMessage = "1 company"
      }

      if (numberOfSubmissions > 1) {
        alertMessage = `${numberOfSubmissions} companies`
      }

      setNotification({
        title: "Payment status updated",
        message: `Payment status is updated for ${alertMessage}`,
        variant: "success",
      })
    }
  }

  return json(null)
}, { authorize: ["es.bahamas.submissions.export.ita"] })

const isExportedOptions = [
  { label: "Yes", value: "true" },
  { label: "No", value: "false" },
]

export default function EconomicSubstanceBahamasItaExportLayout(): ReactNode {
  const { paginatedSubmissions: { data: submissions, totalItemCount } } = useLoaderData<typeof loader>();
  const navigation = useNavigation();
  const location = useLocation()
  const { formMethods } = useFilterForm(submissionsSearchSchema);
  const { columns: submissionColumns } = useEsTbahColumns("ird-export");
  const [rowSelection, setRowSelection] = useState<Record<string, boolean>>({})
  const selectedSubmissionsId = Object.keys(rowSelection)
  const [open, setOpen] = useState(false)
  const [searchParams] = useSearchParams()
  const {
    columns,
    submittedAfterDate,
    submittedBeforeDate,
    companyIncorporatedAfterDate,
    companyIncorporatedBeforeDate,
    financialPeriodStartAt,
    financialPeriodEndAt,
    paidAfterDate,
    paidBeforeDate,
    relevantActivities,
    status,
    showSubmitted,
    allowReopen,
    search,
  } = Object.fromEntries(searchParams) as SubmissionsSearchSchemaType
  const queryParams = queryString.stringify({
    columns,
    submittedAfterDate,
    submittedBeforeDate,
    companyIncorporatedAfterDate,
    companyIncorporatedBeforeDate,
    financialPeriodStartAt,
    financialPeriodEndAt,
    paidAfterDate,
    paidBeforeDate,
    relevantActivities,
    status,
    showSubmitted,
    allowReopen,
    search,
    location: location.pathname,
    submissionIds: Object.keys(rowSelection),
  }, { arrayFormat: "bracket" })
  const isDisabledUpdateStatusButton = Object.entries(rowSelection).length === 0

  return (
    <>
      <CardContainer>
        <Authorized oneOf={["es.bahamas.submissions.search"]}>
          <Form formMethods={formMethods}>
            <FilterRow cols={5}>
              <FormColumnsFilter label="Visible Columns" columns={submissionColumns} />
              <FormDatePicker name="submittedAfterDate" label="Submitted After" />
              <FormDatePicker name="submittedBeforeDate" label="Submitted Before" />
              <FormDatePicker
                name="companyIncorporatedAfterDate"
                label="Company Incorporated After Date"
              />
              <FormDatePicker
                name="companyIncorporatedBeforeDate"
                label="Company Incorporated Before Date"
              />
              <FormDatePicker name="financialPeriodStartAt" label="Financial Period Starts Date" />
              <FormDatePicker name="financialPeriodEndAt" label="Financial Period End Date" />
              <FormSelect
                name="isExported"
                label="Is Exported?"
                selectValueProps={{ placeholder: "All" }}
                options={isExportedOptions.map(items => (
                  <SelectItem key={items.value} value={items.value}>{items.label}</SelectItem>
                ))}
              />

            </FilterRow>
            <FilterRow>
              <div className="col-span-full flex flex-row items-center gap-2">
                <FormSearch
                  name="search"
                  formItemProps={{ className: "w-full" }}
                  inputProps={{ placeholder: "Search entity name, master client, referral office, etc." }}
                />
                <Button size="sm" className="gap-1.5" type="submit">
                  <Filter size={14} />
                  Apply Filter(s)
                </Button>
                <Authorized oneOf={["es.bahamas.submissions.export.ita"]}>
                  <LinkButton
                    buttonProps={{
                      type: "button",
                      variant: "link",
                      size: "sm",
                      className: "gap-1.5",
                      disabled: isDisabledUpdateStatusButton,
                    }}
                    linkProps={{
                      to: {
                        pathname: "/economic-substance/export-ird/file",
                        search: queryParams,
                      },
                      reloadDocument: true,
                    }}
                  >
                    Export as XLSX
                    <Download size={14} />
                  </LinkButton>
                  <LinkButton
                    buttonProps={{
                      type: "button",
                      variant: "link",
                      size: "sm",
                      className: "gap-1.5",
                      disabled: isDisabledUpdateStatusButton,
                    }}
                    linkProps={{
                      to: {
                        pathname: "/economic-substance/export-ird/evidence-files",
                        search: queryParams,
                      },
                      reloadDocument: true,
                    }}
                  >
                    Export Evidence Files
                    <Download size={14} />
                  </LinkButton>
                </Authorized>
              </div>
            </FilterRow>
          </Form>
        </Authorized>
        <EnhancedTableContainer>
          <EnhancedTable
            data={submissions}
            loading={<LoadingState isLoading={navigation.state === "loading"} />}
            onRowClick={(row: Row<SubmissionDTO>) => {
              setRowSelection((prev) => {
                const copy = { ...prev };
                // If the row was selected, action was to deselect it, so remove it from the list.
                row.getIsSelected() ? delete copy[row.id] : (copy[row.id] = true);

                return copy;
              });
            }}
            rowId="id"
            columns={submissionColumns}
            totalItems={totalItemCount}
            reactTableOptions={{
              state: {
                rowSelection,
              },
              onRowSelectionChange: setRowSelection,
              enableSorting: true,
            }}
          />
        </EnhancedTableContainer>
      </CardContainer>
      {totalItemCount && (
        <UpdatePaymentStatusDialog
          open={open}
          onOpenChange={setOpen}
          submissions={submissions as SubmissionDTO[]}
          selectedSubmissions={selectedSubmissionsId}
        />
      )}
    </>
  );
}
