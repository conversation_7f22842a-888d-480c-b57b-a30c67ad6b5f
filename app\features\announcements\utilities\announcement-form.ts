import type { AnnouncementSchemaType } from "../schema/announcement-schema";
import { format, getTime } from "date-fns";
import { formatDateForAPI } from "~/lib/utilities/format";

export function getDefaultValues(announcementData: any): Partial<AnnouncementSchemaType> {
  if (!announcementData) {
    return {
      subject: "",
      emailSubject: "",
      sendNow: undefined,
      scheduledDate: undefined,
      scheduledTime: undefined,
      sendToAllMasterClients: undefined,
      jurisdictionId: "",
      masterClientCodes: [],
      body: "",
    }
  }

  const { sendAt, sentAt, subject, emailSubject, jurisdictionIds, body, masterClientCodes } = announcementData
  const sendNowDefaultValue = sentAt === null ? "false" : "true"
  const scheduledTimeDefaultValue = sendAt ? format(getTime(sendAt), "HH:mm") : undefined
  const scheduledDateDefaultValue = sendAt ? formatDateForAPI(sendAt, { short: true }) : undefined
  const sendToAllMasterClientsDefaultValue = !(masterClientCodes && masterClientCodes.length > 0) ? "true" : "false"
  // Jurisdiction Id logic
  let jurisdictionIdDefaultValue = ""
  if (jurisdictionIds && jurisdictionIds.length > 0) {
    if (jurisdictionIds.length === 1) {
      jurisdictionIdDefaultValue = jurisdictionIds[0]
    }

    if (jurisdictionIds.length > 1) {
      jurisdictionIdDefaultValue = "all"
    }
  }

  return {
    subject: subject ?? "",
    emailSubject: emailSubject ?? "",
    sendNow: sendNowDefaultValue,
    scheduledDate: scheduledDateDefaultValue,
    scheduledTime: scheduledTimeDefaultValue,
    sendToAllMasterClients: sendToAllMasterClientsDefaultValue,
    jurisdictionId: jurisdictionIdDefaultValue,
    masterClientCodes: masterClientCodes ?? [],
    body: body ?? "",
  }
}
