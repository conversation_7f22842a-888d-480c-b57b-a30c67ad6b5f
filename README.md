<p align="center">
  <a href="https://www.netprogroup.com/">
    <img alt="NetPro Group" src="https://www.netprogroup.com/assets/files/logo-blauw.svg" width="200" />
  </a>
</p>

# Trident Trust - Management Portal PCP

This is the repository for the management portal of the Private Client Portal (PCP) project for Trident Trust.

## Documentation & references

- 📖 [Remix docs](https://remix.run/docs)

## Instructions to run project

1. **For Windows users:**

    Connect to the NetPro internal NPM registry by running the following command from the project root:

    ```sh
    # Only needed if you run on Windows
    npx vsts-npm-auth -config .npmrc
    ```

    This will install the vsts-npm-auth package and run it to authenticate with the NetPro NPM registry.
1. Run the following command to install all necessary dependencies:

    ```sh
    npm install
    ```

1. Run the following command to start the project in development mode:

    ```sh
    npm run dev
    ```

## Styling

This project comes with [Tailwind CSS](https://tailwindcss.com/) already configured for a simple default starting experience.

## API Integration

Backend services are automatically generated using `hey-api`. Unfortunately, due to the constraints of the Azure environment, services cannot be automatically generated in production and must be manually generated by the developer. If you find that some backend services are missing in your project, follow these steps:

1. Ensure your local backend is up-to-date and running. Confirm that the required service(s) are available in the [Swagger documentation](https://localhost:7108/swagger/index.html), as this will be used to generate the services for the frontend.
2. Run the command: `npm run generate-api-service`.
3. **Important:** Do not reformat or manually modify the code in `app/services/api-generated/**` as it will be overwritten by future runs.

### Handling Merge Conflicts

Merge conflicts may occur due to this process. If this happens, simply delete the existing generated services and regenerate them, ensuring you are using the latest version of the local API.

## **Forms**
The form components in this application are built with modularity and reusability in mind. These components are organized under `app/components/Form` and leverage a streamlined approach using the `app/lib/makeFormField` utility. This approach reduces boilerplate and provides a consistent interface for handling form logic and UI rendering.

### **Form Element Components**
All form element components (e.g., input, select, etc.) are composed using the `makeFormField` helper function. This utility simplifies integration with `react-hook-form` by taking care of repetitive tasks such as field validation, message rendering, and label association. To get started, refer to the existing `Form...` (eg. `app/components/FormInput`) components for examples and usage patterns.

### **Search Forms**
For pages that include **overview tables** (typically implemented using `app/components/EnhancedTable`), search and filter functionality is often required. This is particularly useful for providing users with a way to refine data in tables dynamically.

#### **Search Parameters**
Search and filter values for these tables are stored in the **URL search parameters**, ensuring persistence and shareability of search results. The `query-string` package is used to handle the serialization and parsing of these parameters.

#### **Filtering Columns**
To manage column-specific filtering, use the `FilterColumns` component located at `app/components/ui/filters/FilterColumns`. This component integrates seamlessly with the search functionality and allows for flexible column-based filtering. For a practical implementation, refer to the `app/routes/_main._card.bo-directors.overview` route.

## EnhancedTable
### Column Ordering
Column ordering is automatically handled by the EnhancedTable component, the component is only responsible for settings the searchParams, the the developer is responsible for mapping the search params to the right API params

If the API provides an enum/union for the sortable columns consider using `app/lib/makeMakeColumn` this utility function can help map the sortable columns into a ColumnDef for the EnhancedTable in a typesafe way.

## Date Handling

For date handling in the management portal, we follow standardized patterns to ensure consistency across all features. The application uses several utilities from `app/lib/utilities/format.ts` and hooks for proper date formatting and timezone handling.

### Core Principles

- **All dates should be saved in UTC format**
- **All dates should be in ISO format when sent to the API**
- **Use standardized utilities and hooks for consistent behavior**
- **Follow established patterns from working implementations**

### Date Formatting Rules

#### **1. Filter Forms (Search/List Views)**
For date filters in search forms and list views:
- **Schema**: Use `optionalDateString` validator
- **FormDatePicker**: Use simple configuration, relies on built-in `formatDateForAPI(value, { short: true })` → YYYY-MM-DD
- **No preprocessing needed**: FormDatePicker handles the formatting automatically

```typescript
// Schema
export const searchSchema = z.object({
  submittedAfter: optionalDateString,
  submittedBefore: optionalDateString,
});

// Form usage
<FormDatePicker name="submittedAfter" label="Submitted After" />
```

#### **2. Form Submissions (Create/Edit Forms)**
For forms that submit data to the API:
- **Schema**: Use `nonNullDate` validator for required dates
- **Preprocessing**: Use `formatDateForAPI(date)` in `onSubmit` function → YYYY-MM-DDTHH:mm:ssZ
- **FormDatePicker**: Simple usage, no custom preprocessing

```typescript
// Schema
export const formSchema = z.object({
  startDate: nonNullDate({ message: "Please provide a start date" }),
  endDate: nonNullDate({ message: "Please provide an end date" }),
});

// Form submission
function onSubmit(data: FormSchemaType) {
  const { startDate, endDate } = data;
  const startDateFormatted = formatDateForAPI(startDate);
  const endDateFormatted = formatDateForAPI(endDate);
  const body = JSON.stringify({
    ...data,
    startDate: startDateFormatted,
    endDate: endDateFormatted
  });
  submit({ data: body }, { method: "post" });
}

// FormDatePicker usage
<FormDatePicker name="startDate" label="Start Date*" />
```

#### **3. Date with Time Components**
For dates that include specific times (like scheduled announcements):
- **Combine date and time**: Use `formatDateForAPI(date, { short: true })` + time string
- **Format**: `YYYY-MM-DDTHH:mm:ssZ`

```typescript
// Example from announcements
if (values.sendNow === "false" && (values.scheduledDate && values.scheduledTime)) {
  const scheduledDate = formatDateForAPI(values.scheduledDate, { short: true })!;
  formData.append("sendAt", `${scheduledDate}T${values.scheduledTime}:00Z`);
}
```

#### **4. Table Column Date Display**
For displaying dates in table columns:
- **Use**: `useFormatColDate` hook with `createColumnHelper` pattern
- **Timezone**: Specify timezone when needed (e.g., `{ timezone: "UTC" }`)

```typescript
export function useTableColumns() {
  const formatColDate = useFormatColDate();
  const columnHelper = createColumnHelper<DataType>();
  const columns = [
    columnHelper.display({
      id: "dateColumn",
      header: "Date",
      enableSorting: true,
      cell: formatColDate("dateField", { timezone: "UTC" }),
    }),
  ];

  return { columns };
}
```

#### **5. Automatic Date Calculations**
For forms with automatic date calculations (like financial periods):
- **Use**: `handleStartDateChange` callback with `useCallback`
- **Pattern**: Calculate end date automatically when start date changes

```typescript
const handleStartDateChange = useCallback((value: Date | undefined) => {
  const startDate = formatDateForAPI(value, { short: true });
  form.setValue("startDate", startDate);
  if (value) {
    const newDate = subDays(addYears(value, 1), 1);
    form.setValue("endDate", formatDateForAPI(newDate, { short: true }));
  }
}, [form]);

// FormDatePicker with custom onChange
<FormDatePicker
  name="startDate"
  label="Start Date*"
  datePickerProps={{
    onChange: (value: Date) => handleStartDateChange(value as Date | undefined),
  } as any}
/>
```

### Key Utilities

#### **formatDateForAPI**
- **Purpose**: Formats dates for API submission
- **Usage**:
  - `formatDateForAPI(date)` → Full ISO format (YYYY-MM-DDTHH:mm:ssZ)
  - `formatDateForAPI(date, { short: true })` → Short format (YYYY-MM-DD)

#### **useFormatColDate**
- **Purpose**: Hook for formatting dates in table columns
- **Usage**: `formatColDate("fieldName", { timezone: "UTC" })`

#### **Validators**
- **nonNullDate**: For required date fields in forms
- **optionalDateString**: For optional date fields in filters

### Best Practices

1. **Follow established patterns**: Use EconomicSubstanceBahamasChangeFinancialPeriod as the reference implementation
2. **Keep FormDatePicker simple**: Avoid complex logic in the component, handle preprocessing in onSubmit
3. **Use consistent schemas**: `nonNullDate` for forms, `optionalDateString` for filters
4. **Centralized formatting**: Always use `formatDateForAPI` for API submissions
5. **Proper error handling**: Check for undefined values when using `formatDateForAPI`

### Migration Notes

When updating existing date handling:
1. Replace manual date formatting with `formatDateForAPI`
2. Update schemas to use standard validators
3. Simplify FormDatePicker usage by removing custom preprocessing
4. Move date formatting logic to onSubmit functions
5. Use the `useFormatColDate` hook for table columns

This standardized approach ensures consistent date handling across all features and eliminates timezone-related bugs.
