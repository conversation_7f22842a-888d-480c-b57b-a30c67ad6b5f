import type { ReactNode } from "react";
import { Button, Label, SelectItem } from "@netpro/design-system";
import { Link, useLoaderData, useNavigation } from "@remix-run/react";
import { CloudUpload, Filter, Wallet } from "lucide-react";
import { useState } from "react";
import { Authorized } from "~/components/Authorized";
import { CardContainer } from "~/components/CardContainer";
import { EnhancedTable } from "~/components/EnhancedTable";
import { EnhancedTableContainer } from "~/components/EnhancedTableContainer";
import { FilterRow } from "~/components/FilterRow";
import { Form } from "~/components/Form";
import { FormColumnsFilter } from "~/components/FormColumnsFilter";
import { FormSearch } from "~/components/FormSearch";
import { FormSelect } from "~/components/FormSelect";
import { LoadingState } from "~/components/ui/filters/LoadingState";
import { UpdatePaymentStatusDialog } from "~/features/dialogs/payments/UpdatePaymentStatusDialog";
import { searchSchema } from "~/features/simplified-tax-return/payments/schemas/search-schema";
import { usePaymentsColumns } from "~/features/simplified-tax-return/utilities/use-payments-columns";
import { authHeaders } from "~/lib/auth/utils/auth-headers";
import { useFilterForm } from "~/lib/hooks/useFilterForm";
import { makeEnhancedAction } from "~/lib/makeEnhancedAction.server";
import { makeEnhancedLoader } from "~/lib/makeEnhancedLoader.server";
import { middleware } from "~/lib/middlewares.server";
import { getFilterParams } from "~/lib/utilities/get-filter-params";
import { Modules } from "~/lib/utilities/modules";
import { requireActiveModule } from "~/lib/utilities/require-active-modules";
import type { ListSubmissionDTO, MarkSubmissionsAsPaidRequestDTO } from "~/services/api-generated";
import { managementListSubmissions, managementMarkAsPaid } from "~/services/api-generated";

export const handle = {
  breadcrumb: {
    label: "Payments",
    to: "/simplified-tax-return/payments",
  },
  title: "Payments",
}

export const loader = makeEnhancedLoader(async ({ request, json, queryString }) => {
  await middleware(["auth"], request);
  const { module: strModule } = await requireActiveModule({ request, key: Modules.SIMPLIFIED_TAX_RETURN });
  const schemaData = searchSchema.safeParse(queryString).data
  const { pageNumber, pageSize, order, orderDirection } = await getFilterParams({ request });
  const { data: paginatedSubmissions, error } = await managementListSubmissions({ headers: await authHeaders(request), query: {
    ModuleId: strModule.id,
    PageNumber: pageNumber,
    GeneralSearchTerm: schemaData?.search,
    SortOrder: orderDirection,
    SortBy: order as any,
    PageSize: pageSize,
    SubmittedAfterDate: "1900-01-01", // This date is to retrieve only the submitted ones
    IsPaid: schemaData?.isPaid === "true" ? true : schemaData?.isPaid === "false" ? false : undefined,
    FinancialYear: Number(schemaData?.filingYear) || undefined,
  } });

  if (error) {
    // Unhandled API error
    console.error("Error fetching payments", error);
    throw new Response("Currently unable to retrieve payments", { status: 412 });
  }

  return json({
    paginatedSubmissions,
  });
}, { authorize: ["str.submissions.view-paid"] });

export const action = makeEnhancedAction(async ({ request, setNotification, json }) => {
  const formData = await request.formData()
  const data = formData.get("data") as string
  const body = JSON.parse(data) as MarkSubmissionsAsPaidRequestDTO
  const { error } = await managementMarkAsPaid({ headers: await authHeaders(request), body })

  if (error) {
    setNotification({ title: "Failed at updating payment status", variant: "error" })
  } else {
    const numberOfSubmissions = body.submissionIds?.length
    if (numberOfSubmissions) {
      let alertMessage = ""

      if (numberOfSubmissions === 1) {
        alertMessage = "1 company"
      }

      if (numberOfSubmissions > 1) {
        alertMessage = `${numberOfSubmissions} companies`
      }

      setNotification({ title: "Payment status updated", message: `Payment status is updated for ${alertMessage}`, variant: "success" })
    }
  }

  // Using the enhanced server method json() return here to commit the manipulated notification cookie
  return json(null)
}, { authorize: ["str.submissions.mark-paid"] });

export default function SimplifiedTaxReturnPaymentsManagement(): ReactNode {
  const { paginatedSubmissions: { data: submissions, totalItemCount } } = useLoaderData<typeof loader>();
  const [open, setOpen] = useState(false)
  const navigation = useNavigation();
  const [rowSelection, setRowSelection] = useState({})
  const selectedSubmissionsId = Object.keys(rowSelection)
  const isDisabledUpdateStatusButton = Object.entries(rowSelection).length === 0
  const { formMethods } = useFilterForm(searchSchema)
  const paymentsColumns = usePaymentsColumns()

  return (
    <CardContainer>
      <Form formMethods={formMethods}>
        <FilterRow cols={5}>
          <FormColumnsFilter label="Visible Columns" columns={paymentsColumns} />
          <FormSelect
            name="filingYear"
            label="Financial Year"
            selectValueProps={{ placeholder: "All" }}
            options={[2019, 2020, 2021, 2022, 2023, 2024].map(p => (
              <SelectItem key={p} value={`${p}`}>{p}</SelectItem>
            ))}
          />
          <FormSelect
            name="isPaid"
            label="Status"
            selectValueProps={{ placeholder: "All" }}
            options={[{ key: "true", value: "Paid" }, { key: "false", value: "Unpaid" }].map(p => (
              <SelectItem key={p.key} value={p.key}>{p.value}</SelectItem>
            ))}
          />
          <div className="flex gap-2">
            <Authorized oneOf={["str.submissions.mark-paid"]}>
              <div className="flex flex-col justify-end gap-2">
                <Label>Payment Status</Label>
                <Button size="sm" type="button" className="gap-1.5" disabled={isDisabledUpdateStatusButton} onClick={() => setOpen(true)}>
                  <Wallet size={14} />
                  Update Status
                </Button>
              </div>
            </Authorized>
            <Authorized oneOf={["str.payments.import"]}>
              <div className="flex flex-col justify-end gap-2">
                <Label>Payment Records</Label>
                <Button asChild size="sm" type="button" className="gap-1.5">
                  <Link to="/simplified-tax-return/payments/import">
                    <CloudUpload size={14} />
                    Upload File
                  </Link>
                </Button>
              </div>
            </Authorized>
          </div>
        </FilterRow>
        <FilterRow>
          <div className="col-span-full flex flex-row items-center gap-2">
            <FormSearch
              name="search"
              formItemProps={{ className: "w-full" }}
              inputProps={{ placeholder: "Search entity name, master client, referral office, tx id, etc." }}
            />
            <Button size="sm" className="gap-1.5" type="submit">
              <Filter size={14} />
              Apply Filter(s)
            </Button>
          </div>
        </FilterRow>
      </Form>

      <EnhancedTableContainer>
        <EnhancedTable
          data={submissions}
          loading={<LoadingState isLoading={navigation.state === "loading"} />}
          rowId="id"
          columns={paymentsColumns}
          totalItems={totalItemCount}
          reactTableOptions={{
            state: {
              rowSelection,
            },
            onRowSelectionChange: setRowSelection,
            enableSorting: true,
          }}
        />
        {totalItemCount && (
          <UpdatePaymentStatusDialog
            open={open}
            onOpenChange={setOpen}
            submissions={submissions as ListSubmissionDTO[]}
            selectedSubmissions={selectedSubmissionsId}
          />
        )}
      </EnhancedTableContainer>
    </CardContainer>
  );
}
