import type { ReactNode } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  FormControl,
  FormItem,
  FormLabel,
  Form as NetProForm,
  RadioGroupItem,
  Spinner,
} from "@netpro/design-system";
import {
  redirect,
  Form as RemixForm,
  useLoaderData,
  useNavigate,
  useNavigation,
  useParams,
  useSubmit,
} from "@remix-run/react";

import { InfoIcon } from "lucide-react";
import { useEffect, useState } from "react";
import { FormProvider, useForm } from "react-hook-form";
import { FormDatePicker } from "~/components/FormDatePicker";
import { FormRadioGroup } from "~/components/FormRadioGroup";
import { FormTextarea } from "~/components/FormTextarea";
import type { FinancialPeriodSchemaType } from "~/features/economic-substance-tbah/types/financial-period-schema";
import type { ResetToSavedSchemaType } from "~/features/economic-substance-tbah/types/reset-to-saved-schema";
import { resetToSavedSchema } from "~/features/economic-substance-tbah/types/reset-to-saved-schema";
import { Pages } from "~/features/economic-substance-tbah/utilities/form-pages";
import { getUnflattenedDataSet } from "~/features/submissions/utilities/submission-data-set";
import { authHeaders } from "~/lib/auth/utils/auth-headers";
import { makeEnhancedAction } from "~/lib/makeEnhancedAction.server";
import { makeEnhancedLoader } from "~/lib/makeEnhancedLoader.server";
import { middleware } from "~/lib/middlewares.server";
import { formatDateForAPI } from "~/lib/utilities/format";
import {
  managementGetSubmission,
  managementReopenSubmission,
  managementUpdateSubmissionInformation,
} from "~/services/api-generated";

export const loader = makeEnhancedLoader(async ({ request, params, json, setNotification, redirect }) => {
  await middleware(["auth"], request);
  const submissionResponse = await managementGetSubmission({
    headers: await authHeaders(request),
    path: { submissionId: params.id! },
    query: { includeFormDocument: true },
  })
  if (!submissionResponse.data) {
    setNotification({ title: "The requested submission could not be found", variant: "error" })

    return redirect("/economic-substance/submissions")
  }

  const submissionData = getUnflattenedDataSet(submissionResponse.data);
  const financialPeriod = submissionData[Pages.FINANCIAL_PERIOD] as FinancialPeriodSchemaType

  return json({
    submission: {
      startDate: financialPeriod.startDate,
      endDate: financialPeriod.endDate,
    },
  })
}, {
  authorize: ["es.bahamas.submissions.reset"],
});

export const action = makeEnhancedAction(async ({ params, request, setNotification, json }) => {
  await middleware(["auth"], request);
  const { id } = params

  if (!id) {
    throw new Response("The submission ID is required to reopen the submission", { status: 400 });
  }

  const formData = await request.formData()
  const dataBody = JSON.parse(formData.get("data") as string) as ResetToSavedSchemaType
  const { correctStartDate, correctEndDate, comments } = dataBody

  if (correctStartDate && correctEndDate) {
    const { error } = await managementUpdateSubmissionInformation({
      headers: await authHeaders(request),
      path: { submissionId: id },
      body: { startAt: correctStartDate, endAt: correctEndDate },
    })

    if (error) {
      setNotification({ title: error.exceptionMessage as string, variant: "error" })

      return json({ success: false, error: error.exceptionMessage })
    }
  }

  const { error } = await managementReopenSubmission({
    headers: await authHeaders(request),
    path: { submissionId: id },
    body: {
      submissionId: id,
      comments,
    },
  });

  if (error) {
    setNotification({ title: "Failed to reopen submission.", variant: "error" })
  } else {
    setNotification({ title: "Submission is reopen for selected company." })
  }

  return redirect(`/economic-substance/submissions/${id}`)
}, { authorize: ["es.bahamas.submissions.reset"] });

export default function EconomicSubstanceBahamasResetSubmissionStatus(): ReactNode {
  const { id } = useParams()
  const { submission } = useLoaderData<typeof loader>();
  const submit = useSubmit()
  const navigate = useNavigate()
  const navigation = useNavigation();
  const isSubmitting = navigation.state === "submitting" || navigation.state === "loading";
  const [isOpen, setIsOpen] = useState(false);
  const form = useForm<ResetToSavedSchemaType>(
    {
      resolver: zodResolver(resetToSavedSchema),
      defaultValues: {
        comments: "",
        correctFinancialPeriod: undefined,
        correctEndDate: undefined,
        correctStartDate: undefined,
      },
    },
  )
  const { watch, setValue } = form
  const { correctFinancialPeriod } = watch()
  const onSubmit = (data: ResetToSavedSchemaType) => {
    const { correctStartDate, correctEndDate } = data;
    const startDate = correctStartDate ? formatDateForAPI(correctStartDate) : undefined;
    const endDate = correctEndDate ? formatDateForAPI(correctEndDate) : undefined;
    const body = JSON.stringify({
      ...data,
      correctStartDate: startDate,
      correctEndDate: endDate,
    });
    submit({ data: body }, { method: "post" })
  }
  const handleOpenChange = (open: boolean) => {
    if (!open) {
      navigate(`/economic-substance/submissions/${id}`)
    }
  }
  const handleCorrectFinancialPeriodChange = (value: string) => {
    const correctFinancialPeriod = value as ResetToSavedSchemaType["correctFinancialPeriod"]
    setValue("correctFinancialPeriod", correctFinancialPeriod)
    if (correctFinancialPeriod === "true") {
      setValue("correctStartDate", submission.startDate || undefined)
      setValue("correctEndDate", submission.endDate || undefined)
    } else {
      setValue("correctStartDate", undefined)
      setValue("correctEndDate", undefined)
    }
  }

  useEffect(() => {
    setIsOpen(true)
  }, [])

  return (
    <Dialog open={isOpen} onOpenChange={handleOpenChange}>
      <DialogContent>
        <FormProvider {...form}>
          <NetProForm {...form}>
            <RemixForm
              onSubmit={form.handleSubmit(onSubmit)}
              method="post"
              noValidate
            >
              <DialogHeader>
                <DialogTitle className="flex flex-row items-center gap-2 pb-2">
                  <InfoIcon className="text-primary" />
                  Are you sure?
                </DialogTitle>
              </DialogHeader>
              <DialogDescription>
                Clicking 'confirm' will re-open the submission for the client to amend changes.
              </DialogDescription>
              <FormTextarea
                name="comments"
                label="Please provide a note for the client*"
                textareaProps={{
                  disabled: isSubmitting,
                }}
              />
              <FormRadioGroup
                name="correctFinancialPeriod"
                label="Do you want to correct the Financial Period?"
                radioGroupProps={{ onValueChange: handleCorrectFinancialPeriodChange }}
              >
                <FormItem className="flex items-center space-x-2 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="true" />
                  </FormControl>
                  <FormLabel className="font-normal">
                    Yes
                  </FormLabel>
                </FormItem>
                <FormItem className="flex items-center space-x-2 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="false" />
                  </FormControl>
                  <FormLabel className="font-normal">
                    No
                  </FormLabel>
                </FormItem>
              </FormRadioGroup>
              {correctFinancialPeriod === "true" && (
                <>
                  <FormDatePicker
                    name="correctStartDate"
                    label="Correct Start Date*"
                    datePickerProps={{
                      disabled: isSubmitting,
                      disabledDates: { after: new Date() },
                    }}
                  />
                  <FormDatePicker
                    name="correctEndDate"
                    label="Correct End Date*"
                    datePickerProps={{
                      disabled: isSubmitting,
                      disabledDates: { after: new Date() },
                    }}
                  />
                </>
              )}
              <DialogFooter className="pt-4">
                <Button
                  disabled={isSubmitting}
                  variant="outline"
                  type="button"
                  onClick={() => handleOpenChange(false)}
                >
                  Cancel
                </Button>
                <Button
                  disabled={isSubmitting}
                  type="submit"
                >
                  {isSubmitting ? <Spinner className="size-4 mx-0 text-white" /> : "Confirm"}
                </Button>
              </DialogFooter>
            </RemixForm>
          </NetProForm>
        </FormProvider>
      </DialogContent>
    </Dialog>
  )
}
