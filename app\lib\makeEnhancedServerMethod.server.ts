import type { NotificationProps } from "@netpro/design-system";
import type {
  ActionFunctionArgs,
  LoaderFunctionArgs,
  TypedResponse,
} from "@remix-run/node";
import type { UserPermissionsCookieShape } from "./cookiePermissions.server";
import { json, redirect } from "@remix-run/node";
import queryString from "query-string";
import { getSession } from "~/lib/auth/utils/session.server";
import type { permissionName } from "~/services/api-generated";
import { getUserPermissions } from "./cookiePermissions.server";
import { notificationsCookie, userPreferences } from "./cookies.server";
import { PAGINATION } from "./hooks/usePaginationParams";
import { makeEnhancedServerMethodAuthorize } from "./makeEnhancedServerMethodAuthorize";

type RedirectFunction = typeof redirect;

type AsyncRedirectFunction = (
  ...args: Parameters<RedirectFunction>
) => Promise<ReturnType<RedirectFunction>>;

type AsyncJsonFunction = <Data>(
  data: Data,
  init?: number | ResponseInit
) => Promise<TypedResponse<Data>>;

export const notificationSessionKey = "notification";

export type SessionNotification = {
  title: string
  message?: string
  variant?: NotificationProps["variant"]
};

type FlashNotification = (args: SessionNotification) => void;

type UserPreferences = {
  tablePageSize: number
}

type CallbackArgs = {
  enhancedURL: URL
  session: Awaited<ReturnType<typeof getSession>>
  json: AsyncJsonFunction
  redirect: AsyncRedirectFunction
  setNotification: FlashNotification
  getNotification: () => SessionNotification | undefined
  getUserPreferences: () => Promise<UserPreferences>
  getUserPermissions: () => Promise<UserPermissionsCookieShape | null>
  queryString: queryString.ParsedQuery
};

type CallbackFn<T> = (args: CallbackArgs & T) => any;
type Init = number | undefined | ResponseInit;

type PatchInitArgs = {
  setNotificationCookieTo?: () => Promise<string>
  setUserPermissionsCookieTo?: () => Promise<string>
  init?: Init
};

type MethodConfig = {
  authorize: permissionName[]
}

async function patchInit({ setNotificationCookieTo, setUserPermissionsCookieTo, init }: PatchInitArgs): Promise<Init> {
  if ([setNotificationCookieTo, setUserPermissionsCookieTo].every(v => v === undefined) || typeof init === "number") {
    return init;
  }

  const getHeaders = (): Headers => {
    if (init?.headers instanceof Headers) {
      return init.headers
    }

    if (typeof init?.headers === "object") {
      const headers = new Headers();
      Object.entries(init?.headers).forEach(([key, value]) => {
        headers.append(key, value);
      });

      return headers
    }

    return new Headers()
  }
  const headers = getHeaders()

  if (setNotificationCookieTo != null) {
    headers.append("Set-Cookie", await setNotificationCookieTo())
  }

  if (setUserPermissionsCookieTo != null) {
    headers.append("Set-Cookie", await setUserPermissionsCookieTo())
  }

  return {
    ...init,
    headers,
  };
}

/**
 * makeEnhancedServerMethod is a utility function that enhances server-side Remix loader or action functions.
 * It provides additional functionality for managing sessions, Notifications, user preferences, and enhanced URLs,
 * while maintaining a consistent API for Remix route handlers.
 *
 * @template TKind - The type of arguments passed to the loader or action function (e.g., `LoaderFunctionArgs` or `ActionFunctionArgs`).
 *
 * @returns {Function} A factory function that accepts a callback and returns an enhanced server method.
 *
 * @example
 * // Example usage in a Remix loader
 * import { makeEnhancedServerMethod } from '~/lib/enhancedServerMethod';
 *
 * const loader = makeEnhancedServerMethod<LoaderFunctionArgs>()(async ({ enhancedURL, session, json }) => {
 *   const data = await fetchData(enhancedURL.searchParams);
 *   const userPreferences = await getUserPreferences();
 *
 *   return json({ data, userPreferences });
 * });
 *
 * @example
 * // Example usage in a Remix action
 * import { makeEnhancedServerMethod } from '~/lib/enhancedServerMethod';
 *
 * const action = makeEnhancedServerMethod<ActionFunctionArgs>()(async ({ redirect, setNotification }) => {
 *   const result = await performAction();
 *
 *   if (result.success) {
 *     setNotification({ title: "Success", message: "Action completed successfully", variant: "success" });
 *     return redirect("/success");
 *   }
 *
 *   setNotification({ title: "Error", message: "Something went wrong", variant: "error" });
 *   return redirect("/error");
 * }, { authorize: ["..."] });
 *
 * @callback CallbackFn
 * @param {object} args - The arguments passed to the callback function.
 * @param {URL} args.enhancedURL - The parsed URL of the request, including query parameters.
 * @param {Session} args.session - The Remix session object, proxied for advanced management.
 * @param {AsyncRedirectFunction} args.redirect - An async wrapper around the `redirect` function, allowing session commits.
 * @param {AsyncJsonFunction} args.json - An async wrapper around the `json` function, allowing session commits.
 * @param {FlashNotification} args.setNotification - Sets a flash Notification in the session.
 * @param {Function} args.getNotification - Retrieves the current flash Notification from the session.
 * @param {Function} args.getUserPreferences - Retrieves user preferences from cookies.
 * @param {queryString.ParsedQuery} args.queryString - Parsed query parameters from the request URL.
 *
 * @typedef {object} SessionNotification
 * @property {string} title - The title of the Notification notification.
 * @property {string} [message] - The optional message of the Notification.
 * @property {NotificationProps["variant"]} [variant="info"] - The variant of the Notification (e.g., "info", "success", "error").
 *
 * @typedef {object} UserPreferences
 * @property {number} tablePageSize - The preferred table page size for the user. Defaults to 25.
 *
 * @example
 * // Example callback function
 * const callback = async ({ enhancedURL, setNotification, json }) => {
 *   const params = enhancedURL.searchParams;
 *   setNotification({ title: "Notice", message: "Processing your request...", variant: "info" });
 *   return json({ success: true, query: params });
 * };
 */
export function makeEnhancedServerMethod<
  TKind extends LoaderFunctionArgs | ActionFunctionArgs,
>() {
  return function makeEnhancedMethod<TCallBack extends CallbackFn<TKind>>(
    cb: TCallBack,
    methodConfig?: MethodConfig,
  ) {
    return async function (args: TKind): Promise<ReturnType<TCallBack>> {
      if (methodConfig?.authorize) {
        await makeEnhancedServerMethodAuthorize({ request: args.request, requiredPermissions: methodConfig.authorize })
      }

      let setNotificationCookieTo: () => Promise<string>;
      let setUserPermissionsCookieTo: () => Promise<string>;
      //
      const cookieHeader = args.request.headers.get("Cookie");
      const session = await getSession(cookieHeader);
      const enhancedURL = new URL(args.request.url)
      const parsedNotificationsCookie = await notificationsCookie.parse(cookieHeader);

      return cb({
        session,
        enhancedURL,
        queryString: queryString.parse(enhancedURL.searchParams.toString(), { arrayFormat: "bracket" }),
        redirect: async (url, init) =>
          redirect(url, await patchInit({ setNotificationCookieTo, setUserPermissionsCookieTo, init })),
        json: async (data, init) => {
          return json(data, await patchInit({ setNotificationCookieTo, setUserPermissionsCookieTo, init }))
        },
        setNotification({ title, variant = "info", message }) {
          setNotificationCookieTo = () => notificationsCookie.serialize({ title, variant, message });
        },
        getNotification() {
          setNotificationCookieTo = () => notificationsCookie.serialize("")

          return parsedNotificationsCookie
        },
        async getUserPermissions() {
          const [permissions, setCookie] = await getUserPermissions({ request: args.request })

          if (setCookie !== null) {
            setUserPermissionsCookieTo = () => new Promise(resolve => resolve(setCookie))
          }

          return permissions
        },
        async getUserPreferences() {
          const cookieHeader = args.request.headers.get("Cookie");
          const values = await userPreferences.parse(cookieHeader).then(data => data).catch(() => ({}))

          return {
            tablePageSize: Number(values?.tablePageSize) || PAGINATION.PAGE_SIZE,
          }
        },
        ...args,
      });
    };
  };
}
