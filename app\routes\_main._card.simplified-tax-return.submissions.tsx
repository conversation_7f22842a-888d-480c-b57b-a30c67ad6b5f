import type { ReactNode } from "react";
import { Button, SelectItem } from "@netpro/design-system";
import { useLoaderData, useLocation, useNavigation, useSearchParams } from "@remix-run/react";
import { Download, Filter } from "lucide-react";
import queryString from "query-string";
import { useMemo } from "react";
import { Authorized } from "~/components/Authorized";
import { CardContainer } from "~/components/CardContainer";
import { EnhancedTable } from "~/components/EnhancedTable";
import { EnhancedTableContainer } from "~/components/EnhancedTableContainer";
import { FilterRow } from "~/components/FilterRow";
import { Form } from "~/components/Form";
import { FormColumnsFilter } from "~/components/FormColumnsFilter";
import { FormCombobox } from "~/components/FormCombobox";
import { FormDatePicker } from "~/components/FormDatePicker";
import { FormSearch } from "~/components/FormSearch";
import { FormSelect } from "~/components/FormSelect";
import { LinkButton } from "~/components/ui/buttons/LinkButton";
import { LoadingState } from "~/components/ui/filters/LoadingState";
import { useStrColumns } from "~/features/simplified-tax-return/hooks/useStrColumns";
import type { SearchSchemaType } from "~/features/simplified-tax-return/schemas/searchSchema";
import { searchSchema } from "~/features/simplified-tax-return/schemas/searchSchema";
import { formYears } from "~/features/simplified-tax-return/types/form-year";
import { authHeaders } from "~/lib/auth/utils/auth-headers";
import { useFilterForm } from "~/lib/hooks/useFilterForm";
import { makeEnhancedLoader } from "~/lib/makeEnhancedLoader.server";
import { middleware } from "~/lib/middlewares.server";
import { getCountryOptions } from "~/lib/utilities/countries";
import { getFilterParams } from "~/lib/utilities/get-filter-params";
import { Modules } from "~/lib/utilities/modules";
import { requireActiveModule } from "~/lib/utilities/require-active-modules";
import type { ListSubmissionDTOPaginatedResponse } from "~/services/api-generated";
import { managementListSubmissions } from "~/services/api-generated";

export const handle = {
  breadcrumb: {
    label: "Simplified Tax Returns",
    to: "/simplified-tax-return/submissions",
  },
  title: "Submissions",
}

export const loader = makeEnhancedLoader(async ({ request, queryString, setNotification, json }) => {
  await middleware(["auth"], request);
  const { module: strModule } = await requireActiveModule({ request, key: Modules.SIMPLIFIED_TAX_RETURN });
  const schemaData = searchSchema.safeParse(queryString).data
  const { pageNumber, pageSize, order, orderDirection } = await getFilterParams({ request });
  const { data: paginatedSubmissions, error } = await managementListSubmissions({ headers: await authHeaders(request), query: {
    ModuleId: strModule.id,
    PageNumber: pageNumber,
    GeneralSearchTerm: schemaData?.search,
    SortOrder: orderDirection,
    SortBy: order as any,
    Country: schemaData?.country,
    PageSize: pageSize,
    FinancialYear: Number(schemaData?.filingYear) || undefined,
    SubmittedAfterDate: schemaData?.submittedAfter,
    SubmittedBeforeDate: schemaData?.submittedBefore,
  } })

  if (error) {
    if (error.code === 8) {
      // Invalid sorting response
      setNotification({
        title: "Invalid data sorting",
        message: `Cannot sort the table by colum ${order}.`,
      })

      return json({
        paginatedSubmissions: { data: [], totalItemCount: 0 } as ListSubmissionDTOPaginatedResponse,
      })
    }

    // Unhandled API error
    console.error("Error fetching submissions", error);
    throw new Response("Currently unable to retrieve Simplified Tax Return submissions", { status: 412 });
  }

  return json({
    paginatedSubmissions,
  });
}, { authorize: ["str.submissions.view"] })

export default function SimplifiedTaxReturnSubmissionsLayout(): ReactNode {
  const { paginatedSubmissions: { data: submissions, totalItemCount } } = useLoaderData<typeof loader>();
  const location = useLocation();
  const countryOptions = useMemo(() => getCountryOptions(), []);
  const navigation = useNavigation();
  const { formMethods } = useFilterForm(searchSchema);
  const { columns: submissionColumns } = useStrColumns("submissions");
  const [searchParams] = useSearchParams()
  const { columns, submittedAfter, submittedBefore, country, filingYear, search } = Object.fromEntries(searchParams) as SearchSchemaType
  const queryParams = queryString.stringify({
    columns,
    submittedAfter,
    submittedBefore,
    search,
    country,
    filingYear,
    location: location.pathname,
  }, { arrayFormat: "bracket" })

  return (
    <CardContainer>
      <Authorized oneOf={["str.submissions.search"]}>
        <Form formMethods={formMethods}>
          <FilterRow cols={5}>
            <FormColumnsFilter label="Visible Columns" columns={submissionColumns} />
            <FormDatePicker name="submittedAfter" label="Submitted After" />
            <FormDatePicker name="submittedBefore" label="Submitted Before" />
            <FormSelect
              name="filingYear"
              label="Financial Year"
              selectValueProps={{ placeholder: "All" }}
              options={formYears.map(p => (
                <SelectItem key={p} value={`${p}`}>{p}</SelectItem>
              ))}
            />
            <FormCombobox
              name="country"
              label="Country"
              options={[
                { label: "No country", value: "no-country" },
                ...countryOptions,
              ]}
              comboboxProps={{
                placeholder: "Select a country",
                searchText: "Search...",
                noResultsText: "No countries found.",
              }}
            />
          </FilterRow>
          <FilterRow>
            <div className="col-span-full flex flex-row items-center gap-2">
              <FormSearch
                name="search"
                formItemProps={{ className: "w-full" }}
                inputProps={{ placeholder: "Search entity name, master client, referral office, etc." }}
              />
              <Button size="sm" className="gap-1.5" type="submit">
                <Filter size={14} />
                Apply Filter(s)
              </Button>
              <Authorized oneOf={["str.submissions.export"]}>
                <LinkButton
                  buttonProps={{
                    type: "button",
                    variant: "link",
                    size: "sm",
                    className: "gap-1.5",
                  }}
                  linkProps={{
                    to: {
                      pathname: "/simplified-tax-return/submissions/export",
                      search: queryParams,
                    },
                    reloadDocument: true,
                  }}
                >
                  Export as XLSX
                  <Download size={14} />
                </LinkButton>
              </Authorized>
            </div>
          </FilterRow>
        </Form>
      </Authorized>

      <EnhancedTableContainer>
        <EnhancedTable
          sheetURL={row => `/simplified-tax-return/submissions/${row.id}`}
          returnURL="/simplified-tax-return/submissions"
          data={submissions}
          loading={<LoadingState isLoading={navigation.state === "loading"} />}
          rowId="id"
          columns={submissionColumns}
          totalItems={totalItemCount}
          defaultOpen={/^\/simplified-tax-return\/submissions\/./.test(location.pathname)}
        />
      </EnhancedTableContainer>
    </CardContainer>
  );
}
