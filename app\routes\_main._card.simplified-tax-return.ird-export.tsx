import type { ColumnDef, Row } from "@tanstack/react-table";
import { Button, Checkbox, notify, SelectItem } from "@netpro/design-system";
import { redirect, useLoaderData, useNavigation } from "@remix-run/react";
import { Download, Filter } from "lucide-react";
import { type ReactNode, useState } from "react";
import { CardContainer } from "~/components/CardContainer";
import { EnhancedTable } from "~/components/EnhancedTable";
import { EnhancedTableContainer } from "~/components/EnhancedTableContainer";
import { PageErrorBoundary } from "~/components/errors/PageErrorBoundary";
import { FilterRow } from "~/components/FilterRow";
import { Form } from "~/components/Form";
import { FormColumnsFilter } from "~/components/FormColumnsFilter";
import { FormDatePicker } from "~/components/FormDatePicker";
import { FormSearch } from "~/components/FormSearch";
import { FormSelect } from "~/components/FormSelect";
import { LoadingState } from "~/components/ui/filters/LoadingState";
import { searchSchema } from "~/features/simplified-tax-return/schemas/searchSchema";
import { formYears } from "~/features/simplified-tax-return/types/form-year";
import { useQueryString } from "~/hooks/use-query-string";
import { authHeaders } from "~/lib/auth/utils/auth-headers";
import { useFilterForm } from "~/lib/hooks/useFilterForm";
import { useFormatColDate } from "~/lib/hooks/useFormatColDate";
import { makeEnhancedLoader } from "~/lib/makeEnhancedLoader.server";
import { makeMakeColumn } from "~/lib/makeMakeColumn";
import { middleware } from "~/lib/middlewares.server";
import { getFilterParams } from "~/lib/utilities/get-filter-params";
import { Modules } from "~/lib/utilities/modules";
import { requireActiveModule } from "~/lib/utilities/require-active-modules";
import type { ManagementListSubmissionsData, SubmissionDTO } from "~/services/api-generated";
import { managementListSubmissions } from "~/services/api-generated";

export const handle = {
  breadcrumb: {
    label: "IRD Export",
    to: "/simplified-tax-return/ird-export",
  },
  title: "IRD Export",
}

export const loader = makeEnhancedLoader(async ({ request, queryString, json }) => {
  await middleware(["auth"], request);
  const { module: strModule } = await requireActiveModule({ request, key: Modules.SIMPLIFIED_TAX_RETURN });
  const schemaData = searchSchema.safeParse(queryString).data
  const { pageNumber, pageSize, order, orderDirection } = await getFilterParams({ request });

  // If the user has not selected a filing year, default to the last year in the list.
  if (!schemaData?.filingYear) {
    return redirect(`/simplified-tax-return/ird-export?${new URLSearchParams({ ...queryString, filingYear: `${formYears[formYears.length - 1]}` })}`);
  }

  const { data: paginatedSubmissions, error } = await managementListSubmissions({ headers: await authHeaders(request), query: {
    IsPaid: true,
    ModuleId: strModule.id,
    PageNumber: pageNumber,
    GeneralSearchTerm: schemaData?.search,
    SortOrder: orderDirection,
    // Correctly typing order would require it to be present in the schema as an enum which is too strict for our goals
    SortBy: order as any,
    Country: schemaData?.country,
    PageSize: pageSize,
    FinancialYear: Number(schemaData.filingYear),
    SubmittedAfterDate: schemaData?.submittedAfter,
    SubmittedBeforeDate: schemaData?.submittedBefore,
  } });

  if (error) {
    console.error("Error fetching submissions", error);
    throw new Response("Currently unable to retrieve Simplified Tax Return submissions", { status: 412 });
  }

  return json({
    paginatedSubmissions,
  });
}, { authorize: ["str.submissions.export.ird"] });

type SortableColumns = NonNullable<NonNullable<ManagementListSubmissionsData["query"]>["SortBy"]>;
// Silly (and the only way I know of) way to ensure all type values described by an union type are present in an array during runtime
export const sortableColumnNames = Object.keys({
  Status: null,
  CreatedAt: null,
  SubmittedAt: null,
  ExportedAt: null,
  PaymentMethod: null,
  FinancialYear: null,
  LegalEntityCode: null,
  LegalEntityName: null,
  PaymentReference: null,
  MasterClientCode: null,
  PaymentReceivedAt: null,
  LegalEntityVPCode: null,
} satisfies Record<SortableColumns, null>)
//
const makeColumn = makeMakeColumn<SortableColumns, SubmissionDTO>(sortableColumnNames)
//

function useColumns() {
  const formatColDate = useFormatColDate()
  const columns: ColumnDef<SubmissionDTO>[] = [
    { id: "select", enableSorting: false, enableHiding: false, header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected()
          || (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={(value) => {
          if (value) {
            table.toggleAllPageRowsSelected(true);
          } else {
            table.resetRowSelection(false);
          }
        }}
        className="-translate-x-3 mx-1"
        aria-label="Select all"
      />
    ), cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={value => row.toggleSelected(Boolean(value))}
        aria-label="Select row"
        className="-translate-x-3 mx-1"
      />
    ) },
    makeColumn({ id: "CreatedAt", header: "Date Created", accessorFn: formatColDate("createdAt", { timezone: "Nevis" }) }),
    makeColumn({ id: "SubmittedAt", header: "Submitted Date", accessorFn: formatColDate("submittedAt", { timezone: "Nevis" }) }),
    makeColumn({ id: "ExportedAt", header: "Export Date", accessorFn: formatColDate("exportedAt", { timezone: "Nevis" }) }),
    makeColumn({ id: "FinancialYear", header: "Financial Year", accessorKey: "financialYear" }),
    makeColumn({ id: "LegalEntityCode", header: "Regulatory Code", accessorKey: "legalEntityCode" }),
    makeColumn({ id: "LegalEntityName", header: "Entity Name", accessorKey: "legalEntityName" }),
    makeColumn({ id: "LegalEntityVPCode", header: "VP Code", accessorKey: "legalEntityVPCode" }),
    makeColumn({ id: "MasterClientCode", header: "Master Client Code", accessorKey: "masterClientCode" }),
    makeColumn({ id: "PaymentMethod", header: "Payment Method", accessorKey: "paymentMethod" }),
    makeColumn({ id: "PaymentReceivedAt", header: "Paid Date", accessorFn: formatColDate("paymentReceivedAt") }),
    makeColumn({ id: "PaymentReference", header: "Payment Ref", accessorKey: "paymentReference" }),
    makeColumn({ id: "Status", header: "Status", accessorKey: "status" }),
  ]

  return columns
}

export default function SimplifiedTaxReturnIRDExportLayout(): ReactNode {
  const columns = useColumns()
  const { paginatedSubmissions: { data: submissions, totalItemCount } } = useLoaderData<typeof loader>();
  const navigation = useNavigation();
  const query = useQueryString();
  const { formMethods } = useFilterForm(searchSchema);
  const [rowsSelected, setRowsSelected] = useState<Record<string, boolean>>({});
  const handleExport = async () => {
    try {
      const body = JSON.stringify({
        submissionIds: Object.keys(rowsSelected),
        financialYear: query.filingYear,
      });
      const response = await fetch("/simplified-tax-return/ird-export/file", {
        method: "POST",
        headers: {
          "Accept": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
          "Content-Type": "application/json",
        },
        body,
      });

      if (!response.ok) {
        throw new Error(`Export failed with status ${response.status}: ${response.statusText}`);
      }

      // Get file name from response headers
      const fileName = response.headers.get("Content-Disposition")?.split("filename=")[1];
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = fileName ? fileName.substring(1, fileName.length - 1) : "ird-export.xlsx";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      notify({ variant: "success", message: "File exported successfully!" });
    } catch (error) {
      notify({ variant: "error", message: "Failed to export the file. Please try again." });
    }
  };

  return (
    <CardContainer>
      <Form formMethods={formMethods}>
        <FilterRow cols={4}>
          <FormColumnsFilter label="Visible Columns" columns={columns} />
          <FormDatePicker name="submittedAfter" label="Submitted After" />
          <FormDatePicker name="submittedBefore" label="Submitted Before" />
          <FormSelect
            name="filingYear"
            label="Financial Year"
            selectValueProps={{ placeholder: `${formYears[formYears.length - 1]}` }}
            options={formYears.map(p => (
              <SelectItem key={p} value={`${p}`}>{p}</SelectItem>
            ))}
          />
        </FilterRow>
        <FilterRow>
          <div className="col-span-full flex flex-row items-center gap-2">
            <FormSearch
              name="search"
              formItemProps={{ className: "w-full" }}
              inputProps={{ placeholder: "Search entity name, master client, referral office, etc." }}
            />
            <Button size="sm" className="gap-1.5" type="submit">
              <Filter size={14} />
              Apply Filter(s)
            </Button>
            <Button
              size="sm"
              className="gap-1.5"
              type="button"
              variant="link"
              disabled={Object.keys(rowsSelected).length === 0}
              onClick={handleExport}
            >
              Export as XLSX
              <Download size={14} />
            </Button>
          </div>
        </FilterRow>
      </Form>

      <EnhancedTableContainer>
        <EnhancedTable
          data={submissions}
          loading={<LoadingState isLoading={navigation.state === "loading"} />}
          rowId="id"
          onRowClick={(row: Row<SubmissionDTO>) => {
            setRowsSelected((prev) => {
              const copy = { ...prev };
              // If the row was selected, action was to deselect it, so remove it from the list.
              row.getIsSelected() ? delete copy[row.id] : (copy[row.id] = true);

              return copy;
            });
          }}
          reactTableOptions={{
            state: {
              rowSelection: rowsSelected,
            },
            onRowSelectionChange: setRowsSelected,
          }}
          columns={columns}
          totalItems={totalItemCount}
        />
      </EnhancedTableContainer>
    </CardContainer>
  );
}

export const ErrorBoundary = PageErrorBoundary;
