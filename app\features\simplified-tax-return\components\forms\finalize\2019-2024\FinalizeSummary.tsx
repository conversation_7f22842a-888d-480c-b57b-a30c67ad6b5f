import type { ReactNode } from "react";
import type { FinalizeType } from "~/features/simplified-tax-return/schemas/finalize/2019-2024/finalize-schema";
import { Pages } from "~/features/simplified-tax-return/utilities/form-pages";
import { useSubmission } from "~/features/submissions/context/use-submission";
import { useFormatDate } from "~/lib/hooks/useFormatDate";
import { getCountryName } from "~/lib/utilities/countries";

export function FinalizeSummary(): ReactNode {
  const formatDate = useFormatDate()
  const { submissionData } = useSubmission();
  const finalize = submissionData[Pages.FINALIZE] as FinalizeType;

  return (
    <section id="declaration-section" className="flex flex-col gap-8 font-inter">
      <div>
        <h2 className="text-lg font-semibold">Declaration and Certificate</h2>
        <div className="border border-blue-600 p-2">
          <p>I hereby declare and confirm that:</p>
          <ul className="list-disc list-inside pl-4">
            <li>
              the information given on this form is to the best of my knowledge and belief,
              true and correct and that I have the authority to disclose the information provided.
            </li>
            <li>
              I understand that the Inland Revenue Department reserves the right to review this return and
              I can be held responsible for:
              <ul className="list-[circle] list-inside pl-8">
                <li>
                  understating, overstating or omitting data and
                </li>
                <li>
                  the
                  payment of any fees, fines and penalties associated with these actions, as defined under the
                  Income Tax Act, the Tax Administration and Procedures Act and the Perjury Act of the Revised
                  Laws of St. Kitts and Nevis.
                </li>
              </ul>
            </li>
            <li>
              I am aware that a person who makes a false declaration commits an offence under Section 5 of the
              Perjury Act, of the Revised Laws of St. Kitts and Nevis and that person can upon conviction, be
              liable to imprisonment for a term of not less than seven years and not more than ten years, or
              to a fine of not less than EC$30,000.00 and not more than EC$50,000.00, or both.
            </li>
          </ul>
        </div>
      </div>
      <div>
        <div className="flex flex-col gap-4 border border-blue-600 p-2 ">
          <div className="flex justify-between">
            <div className="flex-1 gap-1">
              <span className="font-semibold ">Name of the person stating the declaration:</span>
              <br />
              <span>{finalize?.nameOfPersonDeclaring}</span>
            </div>
            <div className="flex-1  text-center">
              <span className="font-semibold ">Date:</span>
              {" "}
              <span>{formatDate(finalize?.dateOfSignature as Date)}</span>
            </div>
          </div>

          <div className="">
            <span className="font-semibold ">Address:</span>
            {" "}
            <span>{finalize?.addressOfPersonDeclaring}</span>
          </div>

          <div className="flex justify-between">
            <div>
              <span className="font-semibold ">City:</span>
              {" "}
              <span>{finalize?.city}</span>
            </div>
            <div>
              <span className="font-semibold ">Zip Code:</span>
              {" "}
              <span>{finalize?.zipCode}</span>
            </div>
            <div>
              <span className="font-semibold ">Country:</span>
              {" "}
              <span>{getCountryName(finalize?.country)}</span>
            </div>
          </div>

          <div className="flex flex-col gap-1">
            <span className="font-semibold ">Return Made:</span>
            {finalize?.onMyOwnBehalf && (
              <span>
                On my own behalf:
                {" "}
                {finalize.onMyOwnBehalf}
              </span>
            )}
            {finalize?.asOfficer && (
              <span>
                As officer of a corporate body namely:
                {" "}
                {finalize.asOfficer}
              </span>
            )}
            {finalize?.asAttorney && (
              <span>
                As Attorney, Agent, Accountant, Manager for:
                {" "}
                {finalize.asAttorney}
              </span>
            )}
            {finalize?.asTrustee && (
              <span>
                As Trustee, Executor, Administrator for:
                {" "}
                {finalize.asTrustee}
              </span>
            )}

          </div>
        </div>
      </div>
    </section>
  );
}
